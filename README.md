## 快讯前台列表项目
从ths_news_app项目中迁移出来独立开发部署

## 遇到的坑
Dockerfile必须为Dockerfile，不能命名成DockerFile，否则会在容器镜像里面一直报错

仓库地址: //gitlab.myhexin.com/10jqka/content/zixun/frontend/scene/flashnews
pack地址: //pack.cloud.myhexin.com/10jqka-content-zixun-frontend-scene/flashnews/
web页面访问地址： //news.10jqka.com.cn/app/flashnews_front/index.html


## 开发方案


1. newsList
2. tempNewsList
3. tempList


## 开发流程

### 本地测试

```
npm run serve
```

访问 ``localhost:3333``

### 测试环境部署

1：分支代码合并到testing

2：前往 [http://pack.cloud.myhexin.com/10jqka-content-zixun-frontend-scene/flashnews/](http://pack.cloud.myhexin.com/10jqka-content-zixun-frontend-scene/flashnews/) 

3：测试访问地址

http://news-test.10jqka.com.cn/app/flashnews_front/index.html

### 真机联调

访问文档，安装lightProxy:

[http://**************:8003/pages/viewpage.action?pageId=479068345](http://**************:8003/pages/viewpage.action?pageId=479068345)

配置完毕后，

设置代理：

```
https://news.10jqka.com.cn/app/flashnews_front/ http://news-test.10jqka.com.cn/app/flashnews_front/
```

手机上访问即可