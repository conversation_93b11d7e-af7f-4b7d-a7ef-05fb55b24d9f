

const path = require('path');
const { ELKForWebpackPlugin } = require('@thsf2e/useelk');
//collector采集器地址
const collector = process.env.VUE_CURRENTMODE === 'testing' ? '//khtest.10jqka.com.cn/apm-nginx/skywalking-web/' : '//apm.hexin.cn/skywalking-web/';
module.exports = {
  publicPath: process.env.BASE_URL,
  productionSourceMap: Boolean(process.env.productionSourceMap),

  devServer: {
    port: 3333,
    hot: true,
    hotOnly: true,
    disableHostCheck: true,
  },

  // 插件选项
  pluginOptions: {
    'style-resources-loader': {
      preProcessor: 'less',
      patterns: [
        path.resolve(__dirname, './src/common/style/base.less'),
        path.resolve(__dirname, './src/common/style/colors.less'),
        path.resolve(__dirname, './src/common/style/reset.less'),
        path.resolve(__dirname, './src/common/style/common.less')
      ]
    }
  },

  // webpack配置项
  chainWebpack: config => {
    config.plugin('html').tap(args => {
      args[0].envMode = process.env.VUE_CURRENTMODE;
      return args;
    });
    config.resolve.alias
      .set('@', path.resolve(__dirname, 'src'));
    config.resolve.symlinks(true);
    config.externals({
      'zepto': 'Zepto',
      'vue': 'Vue',
      'thsc-sns-baselib': 'thsc-sns-baselib'
    });
  },

  //自定义webpack配置
  configureWebpack: {
    plugins: [
      new ELKForWebpackPlugin({collector,service:'zixun::website-app-flashnews-frontend'}),
    ],
  }
};
