<template>
  <div class="mask" @touchmove.prevent>
    <div class="loading">
      <img src="//i.thsi.cn/sns/circle/wapcircle/postLib/load-1.2.0-gray.png" alt="" />
      <span>生成中</span>
    </div>
  </div>
</template>

<style lang="less" scoped>
.mask {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  // background: rgba(0, 0, 0, 0.8);
  background: transparent;
  z-index: 100000000;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .loading {
    position: fixed;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    top: 50%;
    left: 50%;
    font-size: 2rem;
    width: 2.5rem;
    height: 2rem;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.5);
    border-radius: 0.2rem;
    z-index: 10000;
    img {
      width: 0.64rem;
      height: auto;
      animation: loading 2s linear infinite;
      z-index: 1000000;
    }
    span {
      margin-top: 0.096rem;
      font-size: 0.32rem;
      color: #fff;
    }

    @keyframes loading {
      to {
        transform: rotate(-360deg);
      }
    }
  }
}
</style>
