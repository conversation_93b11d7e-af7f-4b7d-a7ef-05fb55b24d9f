<template>
  <div>
    <div class="QRArea">
      <div class="qr">
        <div id="share-qrcode" v-show="false"></div>
        <img class="newCode" :src="dataUrl" alt="" />
        <div class="text">
          <div class="top">长按二维码 看更多资讯</div>
          <div class="bottom">知识 · 工具 · 信息 · 社区</div>
        </div>
      </div>
      <div class="cow">
        <img class="needLoading" src="../../assets/<EMAIL>" alt="" />
      </div>
    </div>
    <div class="bottom-bg">
      <img class="slogan needLoading" src="../../assets/<EMAIL>" alt />
    </div>
  </div>
</template>

<script>
import QRCode from 'qrcodejs2';
export default {
  name: 'Bottom',
  props: {
    news: {
      type: Object
    }
  },
  data() {
    return {
      fontSize: document.getElementsByTagName('html')[0].style.fontSize,
      dataUrl: '',
      qrcode: null
    };
  },
  mounted() {
    this.generateQRCode();
  },
  watch: {
    news() {
      this.generateQRCode();
    }
  },
  methods: {
    // 生成二维码
    generateQRCode() {
      const qrcodeUrl = `https://ozone.10jqka.com.cn/spacePage.html?sa=zx_kx_seq_${this.news.seq}`;
      if(!this.qrcode) {
        const rate = 0.55;
        const qrLength = rate * (this.fontSize.split('px')[0]);
        this.qrcode = new QRCode('share-qrcode', {
          width: qrLength,
          height: qrLength, // 高度
          text: qrcodeUrl, // 二维码内容
          colorDark: '#000',      //前景色
          colorLight: '#fff',      //背景色
          correctLevel: QRCode.CorrectLevel.L,     //容错等级
          margin: 10
        });
      } else {
        this.qrcode.clear();
        this.qrcode.makeCode(qrcodeUrl);
      }
      this.$nextTick(()=>{
        const qrCanvs = document.querySelector('#share-qrcode > canvas');
        const qrcBase64 = qrCanvs.toDataURL('image/jpeg');
        this.dataUrl = qrcBase64;
        this.$bus.$emit('handlerCb');
      });
    }
  }
};
</script>

<style lang="less" scoped>
.QRArea {
  position: relative;
  height: 1.73rem;
  width: 100%;
  .qr {
    position: absolute;
    top: 0.15rem;
    left: 0.42rem;
    display: flex;
    justify-content: center;
    align-items: center;
    #share-qrcode {
      display: flex;
      justify-content: center;
      align-items: center;
      background: #fff;
      box-sizing: border-box;
      width: 1.1rem;
      height: 1.1rem;
      img {
        width: 0.56em !important;
        height: 0.56em !important;
      }
    }
    .text {
      font-size: 0.24rem;
      margin-left: 0.19rem;
      color: #707078;
      .top {
        font-family: PingFangSC-Semibold;
        margin-bottom: 0.06rem;
        height: 0.34rem;
        letter-spacing: 0;
      }
      .bottom {
        font-family: PingFangSC-Regular;
      }
    }
  }
  .cow {
    position: absolute;
    right: 0.4rem;
    img {
      width: 1.94rem;
      height: 1.84rem;
    }
  }
}
.bottom-bg {
  height: 1.27rem;
  background-color: #f7f7fc;
  display: flex;
  justify-content: center;
  align-items: center;
  img {
    width: 4.4rem;
    height: 0.4rem;
  }
}
</style>
