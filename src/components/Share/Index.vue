<template>
  <div class="share">
    <div class="shareButton">
      <span v-show="!!news.shareUrl" class="shareIcon" @click="handleShareClick"></span>
      <span v-show="news.shareAmount !== 0" style="margin-left: 0.08rem">
        {{ news.shareAmount > 9999 ? '9999+' : news.shareAmount }}
      </span>
    </div>
    <Loading v-show="loading" />
  </div>
</template>

<script>
import Loading from './Loading.vue';
import { generateCard } from './generateCard';
import { snsInterface } from '@/common/js/interface';
import { warningReport } from '@thsf2e/useelk/lib/report.esm'
let tempSeq = '';
export default {
  name: 'Share',
  components: {
    Loading,
  },
  props: {
    news: {
      type: Object,
    },
  },
  data() {
    return {
      loading: false,
    };
  },
  methods: {
    handleShareClick() {
      const version = Number(window.getAppVersion().replace(/\./g, ''));
      const pType = this.getPlatform();
      if (pType === 'gphone') {
        const GPHONE_VERSION = 105900;
        if(version < GPHONE_VERSION) {
          this.textShare();
        } else {
          this.generate();
        }
      } else if (pType === 'iphone') { // NOSONAR
        const IPHONE_VERSION = 111010;
        if (version < IPHONE_VERSION) {
          this.textShare();
        } else {
          this.generate();
        }
      }
    },
    async generate() {
      this.loading = true;
      if (tempSeq && tempSeq === this.news.seq) {
        await this.showImg()
      } else {
        this.imgShare();
      }
      tempSeq = this.news.seq;
    },
    textShare() {
      const params = {
        title: `【同花顺快讯】${this.news.title}`,
        content: this.news.summary,
        url: this.news.shareUrl,
      };
      snsInterface.hxShare(params);
    },
    imgShare() {
      this.$emit('showDom', async () => {
        await this.showImg();
      });
    },
    async showImg() {
      try{
        let reduceScale = false;
        const titleLength = this.news.title.length;
        const summaryLength = this.news.summary.length;
        const normalTitle = 50;
        const normalSummary = 400;
        const maxTitle = 200;
        // 根据标题和摘要的字数来决定图片清晰度
        reduceScale = !(titleLength < normalTitle && summaryLength < normalSummary);
        const titleMoreThan200 = titleLength > maxTitle;
        const phoneType = this.getPlatform();
        const reduce = {
          reduceScale,
          titleMoreThan200,
          phoneType,
        };

        const mainImageUrl = await generateCard('.shareCard-wrapper', reduce);
        const params = {
          type: '4',
          title: this.news.title,
          content: this.news.summary,
          url: this.news.shareUrl, // 分享渠道中复制链接的url
          // url: this.news.appUrl,
          actionKey: '1',
          showPreview: '1',
          shareInfo: {
            needDefaultQR: false,
            mainImageUrl,
            logoImageUrl: '',
            QRCodeUrl: '',
            shareActionId: 'shareCard',
            screenshotParams: { offsetY: '0', height: '908' },
          },
        };
        snsInterface.hxShare(params);
        this.loading = false;
        this.$sns.funcStat('free_724.auto.liebiao.share');
      }catch(err){
        warningReport({name:'图片分享失败',message:`错误${err}`})
      }
    },
    getPlatform() {
      if (/iPhone|iPad|Mac/.test(navigator.userAgent)) {
        return 'iphone';
      } else if (/Android/) {
        return 'gphone';
      } else {
        return '';
      }
    },
  },
};
</script>

<style lang="less" scoped>
.share {
  .shareButton {
    display: flex;
    align-items: center;
    // margin-left: 0.32rem;
    .shareIcon {
      display: inline-block;
      background-image: url('../../assets/news-share.png');
      background-size: 100% 100%;
      height: 0.32rem;
      width: 0.32rem;
    }
  }
}
</style>
