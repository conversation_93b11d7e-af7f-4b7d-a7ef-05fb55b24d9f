<template>
  <div class="hideDom">
    <div class="shareCard-wrapper">
      <Header />
      <CenterContent :news="news" />
      <Bottom :news="news" />
    </div>
  </div>
</template>

<script>
import Header from './Header.vue';
import CenterContent from './Center.vue';
import Bottom from './Bottom.vue';
export default {
  name: 'NewsDom',
  components: {
    Header,
    CenterContent,
    Bottom,
  },
  props: {
    news: {
      type: Object,
    },
  },
};
</script>

<style lang="less" scoped>
.hideDom {
  position: absolute;
  top: -999999px;
  z-index: -999999;
  .shareCard-wrapper {
    -webkit-font-smooth: subpixel-antialiased;
    background-color: #fff;
  }
}
</style>
