<template>
  <div class="headerArea">
    <img class="headerImg needLoading" src="../../assets/<EMAIL>" alt />
    <img class="titleImg needLoading" src="../../assets/<EMAIL>" alt />
  </div>
</template>

<style lang="less" scoped>
.headerArea {
  position: relative;
  width: 100%;
  height: 2.4rem;
  .headerImg {
    width: 100%;
    height: 100%;
  }
  .titleImg {
    width: 4.65rem;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
</style>
