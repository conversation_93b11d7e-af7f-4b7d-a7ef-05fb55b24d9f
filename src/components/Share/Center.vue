<template>
  <div class="center-wrapper">
    <div class="shareCard-creative">
      <div class="time">
        <span class="name">{{ newsTimeObj.dayTime }}</span>
        <span class="date">{{ newsTimeObj.weekDay }}</span>
        <span class="week">{{ newsTimeObj.newsTime }}</span>
      </div>
    </div>
    <div v-if="!isSimilar" class="shareCard-title" :class="{ 'important-font': news.type === 1, 'ios-font': isIOS }">
      {{ news.title }}
    </div>
    <div class="shareCard-content" ref="shareContent">
      <img class="needLoading" src="../../assets/<EMAIL>" alt />
      <div class="shareContent">
        <p v-html="news.summary" :class="{ 'important-font': news.type === 1 }" />
        <img :src="news.picUrl" crossorigin="anonymous" class="sharePic" alt=""/>
      </div>
    </div>
    <div v-if="stocks.length > 0" class="stockArea">
      <stockTag
        class="stockItem"
        v-for="item in stocks"
        :key="item.stockCode"
        :stock="item"
        :toFixed="2"
        :shareCard="true"
      />
    </div>
    <div class="dash-line" ref="dashLine">
      <img
        class="needLoading"
        src="//i.thsi.cn/sns/circle/wapcircle/post-components-lib/dashed-line.png"
        alt
      />
    </div>
  </div>
</template>

<script>
import stockTag from '@/components/feature/stockTag';
import dayjs from 'dayjs';
import stringSimilarity from 'string-similarity';

// ETF市场代码列表
const ETFMarketMap = [20, 36]; // NOSONAR
// 相似度阈值
const similarThreshold = 0.5;

export default {
  components: {
    stockTag,
  },
  props: {
    news: {
      type: Object,
    },
  },
  watch: {
    news: {
      handler(val) {
        if (!val.stocks.length) {
          this.$nextTick(() => {
            // 因为有无行情标签时的上边距不同(受定位影响),为了保持上边距一致,当没有行情标签时,动态修改上边距
            this.$refs.dashLine.style.marginTop = '0.87rem';
          });
        }
      },
      deep: true,
      immediate: true,
    },
  },
  computed: {
    isIOS() {
      return window.getPlatform() === 'iphone'
    },
    isSimilar() {
      const { title, summary } = { ...this.news };
      const similar = stringSimilarity.compareTwoStrings(title, summary);
      if (similar > similarThreshold) {
        return true;
      }
      return false;
    },
    stocks() {
      const ETFTags = this.news.stocks.filter(item => ETFMarketMap.includes(item.stockMarket));
      // 有ETF标签时只展示第一个ETF标签，无ETF标签时展示前两个标签。
      return ETFTags.length > 0 ? ETFTags.slice(0, 1) : this.news.stocks.slice(0, 2); // NOSONAR
    },
    newsTimeObj() {
      let time = this.news.createTime;

      if (String(time).length === 10) {// NOSONAR
        time = time * 1000; // NOSONAR
      }
      const dayTime = dayjs(time).format('YYYY-MM-DD');
      const newsTime = dayjs(time).format('HH:mm');

      const weekArray = new Array('日', '一', '二', '三', '四', '五', '六'); // NOSONAR
      const week = weekArray[new Date(time).getDay()];

      return {
        newsTime,
        dayTime,
        weekDay: `星期${week}`,
      };
    },
  },
};
</script>

<style lang="less" scoped>
.center-wrapper {
  padding: 0.15rem 0.4rem 0 0.42rem;
  .shareCard-title {
    font-weight: 700;
    font-size: 0.4rem;
    color: #323232;
    text-align: justify;
    letter-spacing: 0.02rem;
    word-break: break-word;
    margin-bottom: 0.38rem;
    &.ios-font {
      font-family: PingFangSC-Medium;
      font-weight: 500;
    }
  }
  .important-font {
    color: #ff2424;
  }
  .shareCard-creative {
    font-size: 0.28rem;
    line-height: 0.56rem;
    letter-spacing: 0;
    margin-bottom: 0.2rem;
    color: #a2a9af;
    .time {
      display: flex;
      .date,
      .week {
        margin-left: 0.5em;
      }
    }
  }
  .shareCard-content {
    font-size: 0.32rem;
    line-height: 0.56rem;
    text-align: justify;
    letter-spacing: 0.0112rem;
    word-break: break-word;
    word-wrap: break-word;
    white-space: normal;
    position: relative;
    img:first-child {
      width: 0.82rem;
      position: absolute;
      top: 0;
      left: -0.18rem;
    }
    p {
      // top: 0.4rem;
      // position: relative;
      z-index: 999;
    }
    .shareContent {
      display: flex;
      flex-direction: column;
      position: relative;
      top: 0.4rem;
      .sharePic {
        width: 6.68rem;
        margin-top: 0.32rem;
      }
    }
  }
  .stockArea {
    font-size: 0.24rem;
    display: flex;
    margin-top: 0.7rem;
    .stockItem {
      margin-right: 0.16rem;
      background-color: #f6f6f6 !important;
    }
  }

  .dash-line {
    margin-top: 0.57rem;
    margin-bottom: 0.5rem;
    img {
      display: block;
      width: 100%;
    }
  }
}
</style>
