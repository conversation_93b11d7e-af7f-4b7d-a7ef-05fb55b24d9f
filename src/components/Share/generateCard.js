import html2canvas from 'html2canvas/dist/html2canvas';

let firstRender = true;

const share = async (dom, commonOptions, extraOptions) => {
  const quality = 1;
  const canvas = await html2canvas(dom, { ...commonOptions, ...extraOptions });
  const base64 = canvas.toDataURL('image/png', quality);
  // 生成base64后将canvas置空,否则在ios上重复点击多次分享后会有异常
  canvas.width = 0;
  canvas.height = 0;
  return base64;
};

// 生成卡片的base64
const getCardBase64 = async (targetDom, reduce) => {
  const isIphone = reduce.phoneType === 'iphone';
  let iphoneScale = 2;
  let gphoneScale = 4;
  if (reduce.reduceScale) {
    iphoneScale = 1;
    gphoneScale = reduce.titleMoreThan200 ? 1 : 2; // NOSONAR
  }
  const dom = document.querySelector(targetDom);
  const domWidth = dom.offsetWidth;
  const canvasOptions = {
    imageTimeout: 0, //设置0关闭超时
    useCORS: true,
    logging: false, // 关闭日志
    width: domWidth,
    backgroundColor: '#fff',
    scale: isIphone ? 1 : gphoneScale, // 可以让图片变得清晰,数字越大生成的图片兆数越大,生成时间也越久
  };
  const textShare = {
    ignoreElements: item => {
      if (item.classList.contains('sharePic')) {
        return true;
      }
      return false;
    },
  };
  let base64 = await share(dom, canvasOptions);
  if (!base64 || base64 === 'data:,') {
    base64 = await share(dom, canvasOptions, textShare);
  }
  return base64;
};

const checkImageLoad = async targetDom => {
  const images = document.querySelector(targetDom).getElementsByClassName('needLoading');
  const promises = Array.prototype.slice.call(images).map(img => {
    return new Promise(resolve => {
      img.addEventListener('load', () => {
        resolve(img);
      });
    }).catch(err => {
      console.log(err);
    });
  });
  try {
    await Promise.all(promises);
    firstRender = false;
  } catch (err) {
    console.log(err); // NOSONAR
  }
};

const generateCard = async (targetDom, reduce) => {
  // 在初次分享时图片资源已加载完毕,所以之后的分享不需要再监听图片是否加载,否则promise.all会一直pending
  if (firstRender) {
    await checkImageLoad(targetDom);
  }
  return getCardBase64(targetDom, reduce);
};

export { generateCard };
