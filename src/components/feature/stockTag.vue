<template>
  <div ref="stock" class="stock" @click="$emit('clickStock')">
    <span v-if="!shareCard" class="stock--image" :class="`stock--${getMarket}`"></span>
    <span v-else class="shareMarket" :class="`share-${getMarket}`">{{ getMarket === 'NONE' ? '' : getMarket }}</span>
    <span ref="stock-name" :class="['stock-name', { 'stock-gray': isread }]">{{ stock.name }}</span>
    <span :class="getStockColor(codeListValues[stock.stockCode])">{{ `${getStockFlag(codeListValues[stock.stockCode])}${num}` }}</span>
  </div>
</template>

<script>
const marketMap = {
  HK: [112, 113, 114, 176, 177, 178, 179, 180, 181, 182, 183],
  US: [168, 169, 170, 171, 172, 184, 185, 186, 187, 188, 189, 200, 201, 202],
  UK: [160, 161],
};

// ETF市场代码列表
const ETFMarketMap = [20, 36]; // NOSONAR

export default {
  name: 'stockTag',
  props: {
    shareCard: {
      type: Boolean,
      default: false
    },
    stock: {
      type: Object,
      require: true,
    },
    isread: {
      type: Boolean,
      default: false,
    },
    toFixed: {
      type: Number,
      default: 3,
    },
    codeListValues: {}
  },
  mounted() {
    // 重置ETF类型标签的最大宽度
    if (ETFMarketMap.includes(this.stock.stockMarket)) {
      this.$refs['stock-name'].style.maxWidth = '3.47rem';
    }
  },
  computed: {
    num() {
      if (!!this.codeListValues[this.stock.stockCode] && Object.prototype.toString.call(+this.codeListValues[this.stock.stockCode]) === '[object Number]') {
        return `${Number(this.codeListValues[this.stock.stockCode]).toFixed(this.toFixed)}%`;
      } else {
        return '--';
      } 
    },
    getMarket() {
      let result = 'NONE';
      Object.entries(marketMap).forEach(([name, ids]) => {
        if (ids.indexOf(Number(this.stock.stockMarket)) > -1) {
          result = name;
        }
      });
      return result;
    },
  },

  methods: {
    // 根据股票涨跌获取类名
    getStockColor(number) {
      if (Object.prototype.toString.call(+number) === '[object Number]') {
        if (number > 0) {
          return 'stock-red';
        } else if (number < 0) {
          return 'stock-green';
        } else {
          if (this.isread) {
            return 'stock-gray';
          } else {
            return 'stock-black';
          }
        }
      } else {
        if (this.isread) {
          return 'stock-gray';
        } else {
          return 'stock-black';
        }
      }
    },
    getStockFlag(number) {
      if (Object.prototype.toString.call(+number) === '[object Number]') {
        if (number > 0) {
          return '+';
        } else {
          return '';
        }
      }
    },
  },
};
</script>

<style lang="less" scoped>
.shareMarket {
  margin-right: 0.04rem;
}
.share-HK {
  color: #5385d9;
}
.share-UK {
  color: #e06677;
}
.share-US {
  color: #d85d91;
}

.stock {
  height: 0.48rem;
  padding: 0 0.16rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  // align-items: baseline;
  line-height: 0.48rem;
  background-color: #f6f6f6;
  border-radius: 0.08rem;
  font-size: 0.24rem;
  &-name {
    color: @66;
    margin-right: 0.12rem;
    max-width: 1.55rem;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  &--image {
    width: 0.36rem;
    height: 0.36rem;
    margin: 0.06rem 0.04rem 0.06rem 0;
    background-size: cover;
  }

  &--NONE {
    display: none;
  }

  &--HK {
    background-image: url(//i.thsi.cn/sns/app-themes-frontend/<EMAIL>);
    color: #5385d9;
    [theme-mode='black'] & {
      background-image: url(//i.thsi.cn/sns/app-themes-frontend/<EMAIL>);
      color: #395c97;
    }
  }

  &--UK {
    background-image: url(//i.thsi.cn/sns/app-themes-frontend/<EMAIL>);
    color: #e06677;
    [theme-mode='black'] & {
      background-image: url(//i.thsi.cn/sns/app-themes-frontend/<EMAIL>);
      color: #9c4753;
    }
  }

  &--US {
    background-image: url(//i.thsi.cn/sns/app-themes-frontend/<EMAIL>);
    color: #d85d91;
    [theme-mode='black'] & {
      background-image: url(//i.thsi.cn/sns/app-themes-frontend/<EMAIL>);
      color: #974165;
    }
  }
}

.stock {
  &-red {
    font-family: THSMoneyfont-Medium;
    color: @red;
  }
  &-green {
    font-family: THSMoneyfont-Medium;
    color: @green;
  }
  &-black {
    color: @32;
  }
  &-gray {
    color: @99;
  }
}
.night {
  .stock {
    background-color: @20;
    &-name {
      color: #a9a9a9;
    }
  }

  .stock {
    &-red {
      color: @red;
    }
    &-green {
      color: @nightgreen;
    }
    &-black {
      color: @nightfont;
    }
    &-gray {
      color: @8e;
    }
  }
}
</style>
