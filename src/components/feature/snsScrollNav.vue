<template>
  <div class="container" ref="scrollnav">
    <div class="tab" @touchstart.stop="handleTouchStart" @touchmove.stop @touchend.stop="handleTouchEnd">
      <div class="tab-pane" v-for="item in tabList" :key="item.id">
        <div
          :class="['tab-pane-block', { 'tab-pane-block-active': item.active }]"
          @click="handleClickTab(item)"
        >
          {{ item.name }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import getEnv from '@ths/getEnv';
const env = getEnv(navigator.userAgent);
export default {
  name: 'snsScrollNav',
  props: {
    tabListData: {
      type: Array,
    },
  },
  data() {
    return {
      tabList: [],
    };
  },
  watch: {
    tabListData(newValue) {
      this.tabList = newValue;
    },
  },
  mounted() {
    this.$nextTick(() => {
      const fz =
        +document.documentElement
          .getAttribute('style')
          .split(' ')[1]
          .replace(/px|;/g, '') / 100;
      const dpr = window.devicePixelRatio || 1;
      const params = {
        x: 0,
        y: 0,
        width: parseInt(document.documentElement.clientWidth * dpr),
        height: parseInt(53 * fz * dpr),
      };
      callNativeHandler('notifyWebHandleEvent', {
        method: 'isWebHandleLeftScroll',
        params,
      });
    });
  },
  methods: {
    handleClickTab(val) {
      this.tabList.forEach(item => {
        item.active = val.id === item.id;
      });
      this.$forceUpdate();
      this.$emit('clickTab', val);
    },
    handleTouchStart() {
      callNativeHandler('notifyWebHandleEvent', {
        method: 'isWebHandleLeftScroll',
        params: {
          isLeftScroll: true,
        },
      });
    },
    handleTouchEnd() {
      callNativeHandler('notifyWebHandleEvent', {
        method: 'isWebHandleLeftScroll',
        params: {
          isLeftScroll: false,
        },
      });
    },
  },
};
</script>

<style lang="less" scoped>
.container {
  position: relative;
  background-color: @ff;
  &::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 0.16rem;
    bottom: 0;
    z-index: 100;
    background-color: @ff;
  }
}
.tab {
  display: flex;
  flex-wrap: nowrap;
  overflow-x: scroll;
  -webkit-overflow-scrolling: touch;
  padding: 0.3rem 0.32rem 0.2rem 0;
  &-pane {
    height: 0.56rem;
    flex-shrink: 0;
    padding-left: 0.32rem;
    &:last-child {
      padding-right: 0.32rem;
    }
    &-block {
      padding: 0 0.2rem;
      height: 0.56rem;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 0.28rem;
      color: @66;
      letter-spacing: 0;
      border-radius: 0.04rem;
      background-color: @f5;
      &-active {
        background-color: @red;
        color: @ff;
      }
    }
  }
}
.night {
  .container {
    background-color: @nightbg;
    &::after {
      background-color: #1c1c1c;
    }
  }
  .tab {
    &-pane {
      &-block {
        color: @nightfont;
        background-color: @32;
        &-active {
          background-color: @nightred;
          color: @ff;
        }
      }
    }
  }
}
</style>
