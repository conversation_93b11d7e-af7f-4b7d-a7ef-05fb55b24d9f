<template>
  <div>
    <div class="load" ref="load">
      <slot></slot>
    </div>
    <div class="nomore" v-show="showTipText">
      <img v-show="tipText === loadingText" class="nomore-img" :src="loadingImg" />
      <span class="nomore-text">{{ tipText }}</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'snsScrollLoad',
  props: {
    scrollEvent: {}, // 滚动事件
    nomore: {
      // 是否开启加载结果判断，若不开启，需要自己控制是否还需要继续加载
      type: Boolean,
      default: true,
    },
    showTipText: {
      // 是否显示加载文案
      type: Boolean,
      default: true,
    },
    preCount: {
      // 余下几条数据时触发加载
      type: Number,
      require: true,
      default: 5,
    },
    judgeMentType: {
      // 没有更多数据的判断方式 ： count按照有无新数据判断   page按照数据不满一页判断
      type: String,
      default: 'count',
    },
    fullFirstScreen: {
      // 数据不足时自动充满1屏
      type: Boolean,
      default: false,
    },
    pageItemCount: {
      // 每页数据条数（如果设置了nomore并且判断条件为page则该项必传）
      type: Number,
      default: 15,
    },
    isLoading: {
      // 是否处于请求状态
      type: Boolean,
    },
    active: {
      // 如果是多tab页面,活动的tab页id需要传入，否则会多次触发滚动事件
      type: Boolean,
      default: true,
    },
    loadingText: {
      // 加载中文案
      type: String,
      default: '正在加载更多',
    },
    loadedText: {
      // 没有更多数据的文案
      type: String,
      default: '- 没有更多了 -',
    },
    loadingImg: {
      type: String,
      default: '//i.thsi.cn/sns/circle/wapcircle/theme/scroll-loading.png',
    },
    mode: {
      type: String,
      default: 'normal',
    },
    nativeCb: {},
    skeleton: {
      // 骨架屏，预留
      type: String,
    },
  },
  data() {
    return {
      flag: true,
      buffer: true, // 缓冲
      childLength: 0,
      diffLength: 0,
      oldDiffLength: 0,
      tipText: '',
      isIndex: false,
      count: 0, // 加载次数
    };
  },
  watch: {
    scrollEvent(val) {
      this.trigger();
    },
    isLoading(val) {
      this.handleLoaded(val);
    },
    nativeCb(data) {
      // 监听到客户端触底，无条件触发加载
      if (data.method == 'scrollHasBeenToBottom') {
        this.nativeTrigger();
      }
    },
  },
  mounted() {
    this.$on('reset', this.reset);
    this.tipText = this.loadingText;
  },
  methods: {
    // 触发load
    trigger() {
      if (this.active) {
        const dom = this.$refs.load;
        if (
          ((dom.children.length > this.preCount &&
            this.judgeDomDisplay(dom.children[dom.children.length - this.preCount])) ||
            (dom.children.length <= this.preCount &&
              this.judgeDomDisplay(dom.children[dom.children.length - 1]))) &&
          this.tipText === this.loadingText
        ) {
          this.count++;
          this.$emit('load', this.count);
        }
      }
    },
    nativeTrigger() {
      this.childLength = this.$refs.load.children.length;
      this.count++;
      this.isIndex = true;
      this.$emit('load', this.count);
    },
    // loading状态改变时触发该函数
    handleLoaded(val) {
      if (this.active) {
        if (!val) {
          if (this.$refs.load.children.length === 0) {
            this.count = 0;
          }
          // 请求完毕，解除标志位限制
          this.flag = true;
          this.$emit('loaded', this.count);
          this.$nextTick(() => {
            // 只有数据增加或数据不变，才判断是否加载完毕，解决手动清空列表后状态错误
            if (this.$refs.load.children.length - this.oldDiffLength >= 0) {
              this.judgeNomore();
            }
            this.oldDiffLength = this.$refs.load.children.length;
            // 若加载完毕后无法触发滚动事件则再发送一次请求
            if (
              !this.buffer &&
              this.fullFirstScreen &&
              this.judgeDomBottom(this.$refs.load.children[this.$refs.load.children.length - 1]) &&
              this.$refs.load.children.length - this.childLength > 0
            ) {
              this.trigger();
            }
            if (this.buffer) {
              this.buffer = false;
              this.trigger();
            }
          });
        } else {
          this.tipText = this.loadingText;
        }
      }
    },
    // 获取DOM的top和bottom高度
    getDOMRect(dom) {
      const rect = dom.getBoundingClientRect();
      const top = document.documentElement.clientTop ? document.documentElement.clientTop : 0;
      return {
        top: rect.top - top,
        bottom: rect.bottom - top,
      };
    },
    // 判断DOM出现在视口内
    judgeDomDisplay(dom) {
      if (dom) {
        const rect = dom;
        const winH =
          window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
        const obj = this.getDOMRect(dom);
        // 如果在加载中已经滑动到触发位置，放入缓冲区，保证不错过请求
        if (obj.top < winH && !this.flag) {
          this.buffer = true;
        }
        if (obj.top < winH && this.flag) {
          // 触发加载后控制一个标志位，保证不多次请求
          this.flag = false;
          this.childLength = this.$refs.load.children.length;
          return true;
        } else {
          return false;
        }
      }
    },
    judgeDomBottom(dom) {
      if (dom) {
        const rect = dom;
        const winH =
          window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
        const obj = dom.getBoundingClientRect();
        if (obj.bottom - winH < 0) {
          return true;
        } else {
          return false;
        }
      }
    },
    judgeNomore() {
      if (this.nomore) {
        // 更新dom之后比较与之前的条数
        this.diffLength = this.$refs.load.children.length;
        // 判断条件：
        // 1. 设置了判断条件为'page': 如果新数据添加数量不足一页或第一次请求数量不足一页，触发nomore
        // 2. 设置了判断条件为'count': 如果没有新数据，触发nomore
        if (
          (this.judgeMentType === 'page' &&
            (this.diffLength - this.childLength < this.pageItemCount ||
              this.diffLength < this.pageItemCount)) ||
          (this.judgeMentType === 'count' && this.diffLength === this.childLength)
        ) {
          this.$set(this, 'tipText', this.loadedText);
          this.$emit('nomore');
        }
      }
    },
    // 重置
    reset() {
      this.tipText = this.loadingText;
      this.childLength = 0;
      this.diffLength = 0;
      this.count = 0;
    },
  },
};
</script>

<style lang="less" scoped>
.nomore {
  height: 0.8rem;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #999;
  &-img {
    width: 0.32rem;
    height: 0.32rem;
    margin-right: 0.12rem;
    animation: rotate 500ms infinite linear;
  }
  &-text {
    font-size: 0.24rem;
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
