<template>
  <div>
    <div
      v-if="!closePulldown"
      class="snsPullDown"
      ref="scroll"
      @touchstart.stop="touchStart"
      @touchmove.stop="touchmove"
      @touchend.stop="touchend"
    >
      <div
        class="snsPullDown-top"
        ref="topscroll"
        :style="{ height: `${diff / 50}rem`, transition: scrollAnimate }"
      >
        <div
          class="snsPullDown-top-container"
          :style="{ marginBottom: `${((topConfig.stayDistance - 14) * 18) / 48 / 50}rem` }"
        >
          <img
            v-show="topText === topConfig.loadingText"
            class="snsPullDown-top-img"
            src="//i.thsi.cn/sns/circle/wapcircle/theme/refresh.png"
            alt=""
          />
          <span>{{ topText }}</span>
        </div>
      </div>
      <div
        ref="tip"
        v-show="showTip && updateTip"
        class="snsPullDown-tip"
        :style="{ height: `${topConfig.stayDistance / 50}rem` }"
      >
        <span>{{ updateStr }}</span>
      </div>
      <div ref="scrollslot">
        <slot></slot>
      </div>
    </div>
    <div v-else>
      <div
        class="snsPullDown-top"
        :style="{ height: `${diff / 50}rem`, transition: scrollAnimate }"
      >
        <div
          class="snsPullDown-top-container"
          :style="{ marginBottom: `${((topConfig.stayDistance - 14) * 18) / 48 / 50}rem` }"
        >
          <img
            v-show="topText === topConfig.loadingText"
            class="snsPullDown-top-img"
            src="//i.thsi.cn/sns/circle/wapcircle/theme/refresh.png"
            alt=""
          />
          <span>{{ topText }}</span>
        </div>
      </div>
      <slot></slot>
    </div>
  </div>
</template>

<script>
import Emitter from '@/common/js/emitter.js';
const TOP_CONFIG = {
  pullText: '下拉刷新',
  triggerText: '释放更新',
  loadingText: '加载中...',
  doneText: '加载完成',
  failText: '加载失败',
  updateText: '为您更新{}条内容',
  loadedStayTime: 700, // 加载成功后停留时间
  refreshStayTime: 300, // 触发刷新页面等待时间
  animateTime: 300,
  stayDistance: 48, // 加载时高度 单位px
  triggerDistance: 55, // 触发高度 单位px
  damp: 4, // 阻尼系数 越大越难拖动
};
export default {
  name: 'snsPullDown',
  data() {
    return {
      state: '',
      startX: 0,
      startY: 0,
      curX: 0,
      curY: 0,
      distance: 0,
      startScrollTop: 0,
      diff: 0,
      topText: '',
      scrollAnimate: '',
      pullLock: false,
      showTip: false,
    };
  },
  mixins: [Emitter],
  props: {
    option: {
      // 覆盖TOP_CONFIG
      type: Object,
    },
    refresh: {
      // 下拉刷新页面
      type: Boolean,
      default: false,
    },
    updateNum: {
      type: Number,
      default: 0,
    },
    updateTip: {
      type: Boolean,
      default: false,
    },
    notice: {
      type: Array,
      require: true,
    },
    noticeDirection: {
      // 通知方向
      type: String,
      default: 'down',
    },
    // notice: [{'通知组件': '通知事件名'}]
    loadMethod: {
      type: Function,
    },
    closePulldown: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    topConfig() {
      return Object.assign({}, TOP_CONFIG, this.option);
    },
    updateStr() {
      if (this.updateNum > 0) {
        const temp = this.topConfig.updateText.split('{}');
        return `${temp[0]}${this.updateNum}${temp[1]}`;
      } else {
        return '暂无更新';
      }
    },
  },
  mounted() {
    this.registerListener();
  },
  methods: {
    registerListener() {
      this.$on('loading', this.actionLoading);
      this.$on('loaded', this.actionLoaded);
    },
    actionPull() {
      this.state = 'pull';
      this.pullLock = true;
      this.topText = this.topConfig.pullText;
    },
    actionTrigger() {
      this.state = 'trigger';
      this.topText = this.topConfig.triggerText;
    },
    // 用第二个参数判断是否是初始化上提操作
    actionLoading(params, isPullup) {
      this.state = 'loading';
      this.topText = this.topConfig.loadingText;
      this.scrollAnimate = `height ${this.topConfig.animateTime}ms ease`;
      // 如果是初始化上提,就不展示动画
      if(!isPullup) {
        this.scrollTo(this.topConfig.stayDistance);
      }
      // this.loadMethod.call(this, this.actionLoaded)
      if (this.refresh) {
        setTimeout(() => {
          location.reload();
        }, this.topConfig.refreshStayTime);
      }
      if (this.noticeDirection === 'up') {
        this.notice.forEach(item => {
          this.dispatch(Object.keys(item)[0], item[Object.keys(item)[0]], params);
        });
      } else if (this.noticeDirection === 'down') {
        this.notice.forEach(item => {
          this.broadcast(Object.keys(item)[0], item[Object.keys(item)[0]], params);
        });
      }
    },
    actionLoaded(loadState = 'done') {
      this.state = 'loaded';
      const { loadedStayTime } = this.topConfig;
      if (loadState === 'done') {
        this.topText = this.topConfig.doneText;
      } else {
        this.topText = this.topConfig.failText;
      }
      this.showTip = true;
      if (this.$refs.tip) {
        this.$refs.tip.style.animation = `snsPullDown-enter ${this.topConfig.animateTime}ms ease forwards`;
      }
      setTimeout(() => {
        this.scrollAnimate = `height ${this.topConfig.animateTime}ms ease`;
        this.scrollTo(0);
        if (this.$refs.tip) {
          this.$refs.tip.style.animation = `snsPullDown-leave ${this.topConfig.animateTime}ms ease forwards`;
        }
        setTimeout(() => {
          this.showTip = false;
          this.pullLock = false;
          if (this.$refs.tip) {
            this.$refs.tip.style.animation = 'none';
          }
        }, this.topConfig.animateTime);
      }, loadedStayTime);
    },
    touchStart(e) {
      this.startX = e.touches[0].clientX;
      this.startY = e.touches[0].clientY;
      this.startScrollTop = this.$refs.scrollslot.getBoundingClientRect().top;
    },
    touchmove(e) {
      const bodyHeight = document.body.getBoundingClientRect().height;
      let h = 0;
      this.curX = e.touches[0].clientX;
      this.curY = e.touches[0].clientY;
      if (bodyHeight < 1000) {
        h = 2;
      } else {
        h = 1;
      }
      if (this.startScrollTop === 0) {
        if (this.curY - this.startY > 0) {
          this.distance = Math.pow((this.curY - this.startY) / (this.topConfig.damp / h), 0.9);
        } else {
          this.distance = 0;
        }
        if (Math.abs(this.curY - this.startY) < Math.abs(this.curX - this.startX)) {
          return;
        }
        if (this.pullLock) {
          e.preventDefault();
        }
        if (this.distance > 0) {
          // 触发时阻止滚动
          e.preventDefault();
          this.scrollAnimate = '';
          this.diff = this.distance;
          if (this.distance < this.topConfig.triggerDistance) {
            this.actionPull();
          } else if (this.distance >= this.topConfig.triggerDistance) {
            this.actionTrigger();
          }
        } else {
          this.diff = 0;
        }
      }
    },
    touchend(e) {
      if (this.diff < this.topConfig.triggerDistance) {
        this.scrollAnimate = `height ${this.topConfig.animateTime}ms ease`;
        this.diff = 0;
        setTimeout(() => {
          this.pullLock = false;
        }, this.topConfig.animateTime);
      }
      if (this.state === 'trigger') {
        this.actionLoading();
      }
    },
    scrollTo(y) {
      this.diff = y;
    },
  },
};
</script>

<style lang="less">
.snsPullDown {
  min-height: 100%;
  &-top {
    // height: 0.96rem;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: flex-end;
    background-color: #f5f5f5;
    color: #999999;
    font-size: 0.24rem;
    overflow: hidden;
    &-container {
      display: flex;
      justify-content: center;
      align-items: center;
    }
    &-img {
      width: 0.44rem;
      height: 0.44rem;
      margin-right: 0.16rem;
      animation: snsPullDown-rotate 500ms infinite linear;
    }
  }
  &-tip {
    position: absolute;
    top: 0;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 0.28rem;
    color: #4691ee;
    background-color: #dae9fc;
    z-index: 3100;
  }
}
.night {
  .snsPullDown {
    &-top {
      background-color: #202020;
      color: #999999;
    }
    &-tip {
      color: #a9afb9;
      background-color: #1e3b5c;
    }
  }
}
@keyframes snsPullDown-rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes snsPullDown-enter {
  0% {
    transform: scale(0.7, 0.7);
    opacity: 0;
  }
  100% {
    transform: scale(1, 1);
    opacity: 1;
  }
}
@keyframes snsPullDown-leave {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-0.8rem);
  }
}
</style>
