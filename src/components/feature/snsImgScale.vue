<template>
  <div id="scaleImg">
    <transition name="fade">
      <div class="bigPicBox" v-if="imginfo.isShow" @click="hideImg" @touchmove.prevent="hideImg">
        <img :src="imginfo.url" ref="imgTag" />
      </div>
    </transition>
  </div>
</template>
<script>
import { sns } from '@/common/js/sns.js';

export default {
  name: 'ScaleImg',
  props: {
    imginfo: Object,
  },
  watch: {
    imginfo: {
      handler(newval) {
        const self = this;
        if (newval.isShow) {
          this.$nextTick(function() {
            sns.loadImg(newval.url, function() {
              // console.log(self.$refs.imgTag)
              sns.scaleImg(self.$refs.imgTag, window.innerWidth, window.innerHeight);
            });
          });
        }
      },
      deep: true,
    },
  },
  methods: {
    hideImg() {
      this.imginfo.isShow = false;
    },
  },
};
</script>
<style lang="less" scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}
.bigPicBox {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  line-height: 100vh;
  text-align: center;
  z-index: 12000;
  background: #000;
  img {
    vertical-align: middle;
  }
}
</style>
