<template>
  <transition name="slide-fade">
    <div class="backToTopButton" v-if="this.show && this.updateCountNum > 0" @click="backToTop">
      <img src="../../assets/news-back.png" v-show="!showNightBackButton" class="backButton" />
      <img src="../../assets/news-back-dark.png" v-show="showNightBackButton" class="backButton" />
      <span class="updateText">有{{ this.updateCountNum > 99 ? '99+' : this.updateCountNum }}条新的快讯</span>
      <!-- <div class="newsNumber" v-show="this.updateCountNum > 0">
        <div :class="{ large: largeNumber }">
          {{ this.updateCountNum > 99 ? 99 : this.updateCountNum }}<sub v-show="this.updateCountNum > 99">+</sub>
        </div>
      </div> -->
    </div>
  </transition>
</template>

<script>
export default {
  name: 'snsBackButton',
  data() {
    return {
      show: false,
      scrollTop: 0,
    };
  },
  props: {
    updateCountNum: {
      type: Number,
      default: 0,
    },
    tabType: {
      type: String,
      default: '',
    },
  },
  mounted() {
    this.$nextTick(() => {
      window.addEventListener('scroll', this.handleScroll);
    });
  },
  computed: {
    showNightBackButton() {
      if (this.$displayMode === 1) {
        return true;
      }
    },
    largeNumber() {
      return this.updateCountNum > 99;
    },
  },
  methods: {
    backToTop() {
      //返回顶部
      //testeafad
      this.show = false;
      window.scroll(0, 0);
      this.$sns.funcStat(`free_zixun_724.up.${this.tabType}`);
    },
    handleScroll() {
      const headPosition = document.getElementsByClassName('header')[0].getBoundingClientRect().top;
      const itemPosition = headPosition !== undefined ? headPosition : 80;
      if (itemPosition === 0) {
        this.show = true;
      } else {
        this.show = false;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.slide-fade-enter-active {
  transition: all 0.1s ease;
}
.slide-fade-enter {
  transform: translateY(50px);
  opacity: 0;
}
.slide-fade-leave-active {
  transition: all 0.1s cubic-bezier(1, 0.5, 0.8, 1);
}

.backToTopButton {
  position: fixed;
  text-align: center;
  top: 0.88rem;
  left: 0;
  right: 0;
  margin: 0 auto;
  height: 0.56rem;
  line-height: 0.52rem;
  width: 2.46rem;
  background-image: linear-gradient(135deg, #fa472f 0%, #e93030 100%);
  box-shadow: 0 0.04rem 0.08rem 0 rgba(233, 48, 48, 0.2);
  border-radius: 0.32rem;
  z-index: 999;

  img {
    width: 0.24rem;
    height: 0.24rem;
    line-height: 0.34rem;
    margin-right: 0.01rem;
  }
  .updateText {
    font-size: 0.24rem;
    color: #fff;
    letter-spacing: 0.01rem;
    height: 0.34rem;
    line-height: 0.34rem;
    vertical-align: middle;
  }
  .newsNumber {
    position: absolute;
    display: inline-block;
    top: -0.08rem;
    right: -0.08rem;
    height: 0.32rem;
    border-radius: 0.16rem;
    background-color: #fd4332;
    text-align: center;
    color: #ffffff;
    font-size: 0.2rem;
    line-height: 0.32rem;
    padding: 0 0.1rem;
  }
  .large {
    line-height: 0.28rem !important;
  }
}
.night {
  .backToTopButton {
    background-image: linear-gradient(-45deg, #ba2626 0%, #c83926 100%);
    .updateText {
      color: #d2d2d3;
    }
  }
}
</style>
