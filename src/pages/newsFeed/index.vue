<template>
  <div class="main">
    <NewsDom v-if="showDom" :news="news" />
    <snsScrollLoad
      :scrollEvent="scroll"
      :nomore="true"
      :showTipText="true"
      :isLoading="isLoading"
      :preCount="5"
      :pageItemCount="20"
      :nativeCb="nativeCb"
      @load="handleScrollLoad"
      @nomore="handleNomoreLoad"
    >
      <newsFeedItem
        ref="newsFeedItem"
        v-for="(item, index) in newsList"
        :index="index"
        :key="item.seq"
        :news="item"
        :newsList="newsList"
        :lastTime="index > 0 ? +newsList[index - 1].ctime : 0"
        :scroll="scroll"
        :curTab="curTab"
        :codeListValues="codeListValues"
        @showDom="handleShowDom(arguments)"
      ></newsFeedItem>
    </snsScrollLoad>
  </div>
</template>

<script>
import NewsDom from '@/components/Share/NewsDom.vue';
import newsFeedItem from './newsFeedItem.vue';
import snsScrollLoad from '@/components/feature/snsScrollLoad';
export default {
  name: 'newsFeed',
  components: {
    newsFeedItem,
    snsScrollLoad,
    NewsDom,
  },
  props: {
    newsList: {
      type: Array,
      default: [],
    },
    scroll: {
      type: Number,
    },
    isLoading: {
      type: Boolean,
    },
    curTab: {},
    nativeCb: {},
    codeListValues: {}
  },
  data() {
    return {
      news: null,
      showDom: false,
      cb: null,
    };
  },
  methods: {
    handleShowDom(arr) {
      this.news = arr[1];
      if (!this.showDom) {
        this.showDom = true;
      }
      this.cb = arr[0];
    },
    handleScrollLoad(page) {
      this.$emit('load', page);
    },
    handleNomoreLoad() {
      this.$sns.funcStat(`${this.$pid}.bottom`);
    },
  },
  mounted() {
    this.$bus.$on('handlerCb', async () => {
      await this.cb();
    });
  },
};
</script>

<style lang="less" scoped>
.main {
  margin-top: 0.12rem;
}
</style>
