<template>
  <transition name="show-item">
    <div ref="container" class="container">
      <div ref="time" class="time" :class="{ 'ios-font': isIOS }" v-show="news.isFirstNew && index !== 0">
        <div style="height: 0.48rem;">{{ newsTimeObj.dayTime }}</div>
        <div class="time-week" style="font-size: 0.24rem">I</div>
        <div class="time-week" style="font-size: 0.28rem">{{ newsTimeObj.weekDay }}</div>
      </div>
      <div class="item" :index="index">
        <div class="item-timeline">
          <div :class="['item-timeline-circle', { 'important-circle': news.type == 1 }]"></div>
          <div class="item-timeline-tick"></div>
        </div>
        <div class="item-body">
          <div :class="['item-body-time', { 'important-font': news.type == 1 }]">
            <span class="item-body-time-font">{{ newsTimeObj.newsTime }}</span>
            <span class="item-body-time-tag" v-show="showTag">异动</span>
          </div>
          <div
            ref="title"
            :class="[
              'item-body-title',
              { 'ios-font': isIOS },
              { 'important-font': news.type == 1 },
              { 'important-read': isread || clickRead },
              { 'important-read-red': news.type == 1 && (isread || clickRead) },
            ]"
          >
            <span @click="handleNewsClick(news)">{{ news.title }}</span>
          </div>
          <div
            :class="[
              'item-body-digest',
              { 'important-font-p70': news.color == 2 },
              { 'important-read': isread || clickRead },
              { 'important-read-red': news.color == 2 && (isread || clickRead) },
            ]"
          >
            <p :class="{ 'item-body-digest-expand': expand }" ref="parse" @click="handleNewsExpand(news)">
              {{ news.summary }}
            </p>
          </div>
          <div
            v-if="news.picUrl"
            class="item-body-picBox"
            :style="{
              backgroundImage: 'url(' + news.picUrl + ')',
              backgroundSize: 'contain',
              backgroundRepeat: 'no-repeat',
              height: news.realImageHeight + 'rem',
            }"
            @click="previewImage"
          ></div>
          <div v-if="stocks.length > 0" class="item-body-stock">
            <stockTag
              class="item-body-stock-tag"
              v-for="item in stocks"
              :key="item.stockCode"
              :stock="item"
              :isread="isread || clickRead"
              :toFixed="2"
              :codeListValues="codeListValues"
              @clickStock="handleStockClick(item)"
            >
            </stockTag>
          </div>
          <div :class="['item-body-opt', { 'item-body-opt-last': news.isLastNew }]">
            <div class="item-body-opt-theme"></div>
            <div class="item-body-opt-right">
              <div class="item-body-opt-right-read" @click="handleNewsClick(news)">
                <span class="item-body-opt-readIcon"></span>
                <span v-show="news.readAmount !== 0" style="margin-left: 0.08rem">{{
                  news.readAmount > 9999 ? '9999+' : news.readAmount
                }}</span>
              </div>
              <div class="item-body-opt-right-share">
                <Share :news="news" @showDom="handleShowDom($event, news)" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
import stockTag from '@/components/feature/stockTag';
import Share from '@/components/Share/Index.vue';
import Emitter from '@/common/js/emitter.js';
import { snsInterface } from '@/common/js/interface';

// ETF市场代码列表
const ETFMarketMap = [20, 36]; // NOSONAR

export default {
  name: 'newsFeedItem',
  mixins: [Emitter],
  components: {
    stockTag,
    Share
  },
  props: {
    news: {
      type: Object,
    },
    newsList: {
      type: Object,
    },
    index: {
      type: Number,
    },
    lastTime: {
      type: Number,
    },
    scroll: {
      type: Number,
    },
    curTab: {},
    codeListValues: {}
  },
  data() {
    return {
      clickRead: false,
      expand: false,
      oldTop: null,
    };
  },
  computed: {
    newsTimeObj() {
      let time = this.news.createTime;
      let hour = '';
      let minute = '';

      if (String(this.news.createTime).length === 10) {
        time = this.news.createTime * 1000;
      }

      const year = new Date(time).getFullYear();
      const month = this.addTimeZero(new Date(time).getMonth() + 1);
      const day = this.addTimeZero(new Date(time).getDate());

      const weekArray = new Array('日', '一', '二', '三', '四', '五', '六');
      const week = weekArray[new Date(time).getDay()];

      hour = new Date(time).getHours();
      minute = new Date(time).getMinutes();

      return {
        newsTime: `${this.addTimeZero(hour)}:${this.addTimeZero(minute)}`,
        dayTime: `${year}年${month}月${day}日`,
        weekDay: `星期${week}`,
      };
    },
    showTag() {
      return this.news.tags.some(item => item.name === '异动');
    },
    isread() {
      if (localStorage.getItem('isRead')) {
        const arr = JSON.parse(localStorage.getItem('isRead'));
        if (arr.filter(x => x === this.news.seq).length > 0) {
          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    },
    stocks() {
      const ETFTags = this.news.stocks.filter(item => ETFMarketMap.includes(item.stockMarket));
      // 有ETF标签时只展示第一个ETF标签，无ETF标签时展示前两个标签。
      return ETFTags.length > 0 ? ETFTags.slice(0, 1) : this.news.stocks.slice(0, 2); // NOSONAR
    },
    isLong() {
      const fz = +document.getElementsByClassName('fontinit')[0].style.fontSize.slice(0, -2);
      if (this.$refs.parse.offsetHeight / (50 * 0.4 * (fz / 100)) > 3) {
        return true;
      } else {
        return false;
      }
    },
    isIOS() {
      return this.getPlatform() === 'iphone'
    }
  },
  watch: {
    scroll: {
      handler(newVal, oldVal) {
        if (this.news.isFirstNew) {
          const pre = 50;
          const { top } = this.$refs.time.getBoundingClientRect();
          if (newVal > 0) {
            if (this.oldTop) {
              if (top - pre > 0 && this.oldTop - pre < 0) {
                // 赋值上一天
                this.dispatch('Main', 'SET_HEAD_TIME', this.lastTime * 1000);
              } else if (top - pre < 0 && this.oldTop - pre > 0) {
                // 赋值当天
                this.dispatch('Main', 'SET_HEAD_TIME', this.news.createTime * 1000);
              }
            }
            this.oldTop = top;
          }
          if (newVal == 0) {
            this.dispatch('Main', 'SET_HEAD_TIME', this.newsList[0].createTime * 1000);
            this.oldTop = 0;
          }
        }
      },
    },
  },
  mounted() {
    // 判断展开收起状态
    this.$nextTick(() => {
      this.initExpand();
    });
    // this.$on('emitPosition', this.emitPosition)
  },
  methods: {
    handleShowDom(fn, news) {
      this.$emit('showDom', fn, news);
    },
    initExpand() {
      this.expand = this.isLong;
    },
    previewImage() {
      this.$sns.funcStat(`${this.$pid}.${this.curTab.type}.openpicture`);
      const reg = /^(http:|https:)/;
      let detailImages = [];
      let originImages = [];
      const imageUrl = this.news.picUrl.replace(reg, window.location.protocol);
      originImages.push(imageUrl);
      detailImages.push(imageUrl.replace(/(_small|_middle)/g, ''));
      window.callNativeHandler(
        'displayImageThumbnail',
        {
          currentIndex: 0,
          originImages: originImages,
          detailImages: detailImages,
        },
        () => {}
      );
    },
    handleNewsClick(news) {
      this.isReadNews(news);
      this.$sns.jumpStat(`${this.$pid}.${this.curTab.type}.des`, `seq_${this.news.seq}`);
      this.jumpToLink(this.news.appUrl);
    },
    handleNewsExpand(news) {
      if (this.isLong) {
        this.expand = !this.expand;
        if (this.expand) {
          this.isReadNews(news);
        } else {
          this.$sns.funcStat(`${this.$pid}.${this.curTab.type}.open`);
        }
      } else {
        this.isReadNews(news);
      }
    },
    // handleShareClick(news) {
    //   const version = Number(window.getAppVersion().replace(/\./g, ''));
    //   console.log('客户端版本号为:', version)
    //   if (this.getPlatform() === 'gphone') {
    //     if(version && version < 105701) {
    //       this.textShare();
    //     } else {
    //       this.$emit('share-card', news);
    //     }
    //   } else if (this.getPlatform() === 'iphone') {
    //     this.textShare();
    //     // if (version < 105010) {
    //     // } else {
    //     //     this.imgShare()
    //     // }
    //   }
    // },
    textShare() {
      const params = {
        title: `【同花顺快讯】${this.news.title}`,
        content: this.news.summary,
        url: this.news.shareUrl,
      };
      snsInterface.hxShare(params);
    },
    imgShare() {
      const params = {
        entranceType: 'newsFlashShare',
        date: this.$base.utils.formatTime(Number(this.news.createTime), '{YYYY}-{MM}-{DD} {hh}:{mm}'),
        tag: this.curTab.name === '全部' ? '精选' : this.curTab.name,
        title: this.news.title,
        content: this.news.summary,
        QRCodeUrl: '',
        shareActionId: '',
      };
      callNativeHandler('notifyWebHandleEvent', {
        method: 'flashNewsShare',
        params,
      });
    },
    // 点击股票跳转
    handleStockClick(stock) {
      this.$sns.jumpStat(`${this.$pid}.${this.curTab.type}.stock`, '', '', {
        to_frameid: 2205,
        to_scode: stock.stockCode,
      });
      this.jumpToStock(stock.stockCode, stock.stockMarket);
    },
    addTimeZero(time) {
      if (time < 10) {
        return `0${time}`;
      } else {
        return time;
      }
    },
    isReadNews(news) {
      this.clickRead = true;
      if (localStorage.getItem('isRead')) {
        const arr = JSON.parse(localStorage.getItem('isRead'));
        if (arr.filter(x => x === news.seq).length === 0) {
          arr.push(news.seq);
          localStorage.setItem('isRead', JSON.stringify(arr));
        }
      } else {
        localStorage.setItem('isRead', JSON.stringify([news.seq]));
      }
    },
    jumpToLink(link) {
      const url = this.$sns.httpUrl(link);
      const ua = navigator.userAgent;
      if (ua.match(/IHexin/)) {
        window.location.href = `client.html?action=ymtz^url=${url}^webid=2804^fontzoom=no`;
      } else if (ua.match(/Hexin_Gphone/)) {
        window.location.href = `client.html?action=ymtz^url=${url}^webid=2804^mode=new^fontzoom=no`;
      } else {
        window.location.href = url;
      }
    },
    jumpToStock(code, marketid) {
      if (!code || !marketid) return;
      const ua = navigator.userAgent;
      if (ua.match(/IHexin|Hexin_Gphone/)) {
        window.location.href = `client.html?action=ymtz^stockcode=${code}^webid=2205^marketid=${marketid}`;
      }
    },
    getPlatform() {
      if (/iPhone|iPad|Mac/.test(navigator.userAgent)) {
        return 'iphone';
      } else if (/Android/) {
        return 'gphone';
      } else {
        return '';
      }
    },
  },
};
</script>

<style lang="less" scoped>
.container {
  padding: 0 0.32rem 0 0.32rem;
  background-color: @ff;
}
.time {
  padding-left: 0.08rem;
  padding-bottom: 0.32rem;
  height: 0.48rem;
  line-height: 0.48rem;
  display: flex;
  align-items: center;
  font-size: 0.3rem;
  color: @32;
  font-weight: 600;

  &.ios-font {
    font-weight: 500;
    font-family: PingFangSC-Medium;
  }
  &-week {
    margin-left: 0.08rem;
    color: @99;
    height: 0.48rem;
    font-weight: 400;
    display: flex;
    align-items: center;
  }
}
.item {
  position: relative;
  padding-left: 0.32rem;
  &-timeline {
    &-circle {
      position: absolute;
      top: 0.16rem;
      left: 0;
      width: 0.16rem;
      height: 0.16rem;
      border-radius: 50%;
      background: @dd;
      z-index: 2;
    }
    &-tick {
      position: absolute;
      top: 0.135rem;
      left: 0.068rem;
      width: 0.02rem;
      height: 100%;
      background: @ee;
      z-index: 1;
    }
  }
  &-body {
    &-time {
      height: 0.48rem;
      display: flex;
      align-items: center;
      font-size: 0.24rem;
      color: @66;
      &-font {
        height: 0.34rem;
        line-height: 0.34rem;
      }
      &-tag {
        display: inline-block;
        width: 0.52rem;
        height: 0.28rem;
        border-radius: 0.04rem 0.04rem 0.04rem 0;
        background: rgba(250, 59, 50, 0.1);
        color: #fa3b32;
        font-size: 0.18rem;
        line-height: 0.28rem;
        text-align: center;
        margin-left: 0.08rem;
        font-weight: normal;
      }
    }
    &-title {
      padding: 0.08rem 0 0.08rem 0;
      color: @32;
      line-height: 0.46rem;
      font-size: 0.34rem;
      font-weight: 600;
      word-break: break-word;
      text-align: left;

      &.ios-font {
        font-family: PingFangSC-Medium;
        font-weight: 500;
      }
    }
    &-digest {
      padding: 0.08rem 0 0.08rem 0;
      line-height: 0.44rem;
      font-size: 0.28rem;
      color: @66;
      p {
        word-break: break-word;
        text-overflow: ellipsis;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
      &-expand {
        display: -webkit-box;
      }
    }
    &-picBox {
      padding: 0.08rem 0 0.08rem 0;
      width: 3.4rem;
    }
    &-stock {
      height: 0.48rem;
      display: flex;
      padding: 0.08rem 0 0.08rem 0;
      &-tag {
        margin-right: 0.16rem;
      }
    }
    &-opt {
      height: 0.52rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 0.2rem;
      padding-bottom: 0.32rem;
      &-last {
        padding-bottom: 0 !important;
        margin-bottom: 0.4rem !important;
      }
      &-theme {
        display: flex;
        &-tag {
          margin-right: 0.12rem;
        }
      }
      &-right {
        display: flex;
        height: 100%;
        max-width: 4rem;
        font-size: 0.2rem;
        color: #666666;
        img {
          width: 0.32rem;
          height: 0.32rem;
        }
        &-read {
          display: flex;
          align-items: center;
        }
        &-share {
          display: flex;
          align-items: center;
          margin-left: 0.32rem;
        }
      }
      &-readIcon {
        display: inline-block;
        background-image: url('../../assets/reading-number.png');
        background-size: 100% 100%;
        height: 0.32rem;
        width: 0.32rem;
      }
      &-shareIcon {
        display: inline-block;
        background-image: url('../../assets/news-share.png');
        background-size: 100% 100%;
        height: 0.32rem;
        width: 0.32rem;
      }
    }
  }
}
.important {
  &-circle {
    background-color: #e93030 !important;
  }
  &-font {
    color: @red;
  }
  &-font-p70 {
    color: @red-p70;
  }
  &-read {
    color: @99!important;
  }
  &-read-red {
    color: @red-p70!important;
  }
}
.night {
  .container {
    background-color: #1c1c1c;
  }
  .time {
    color: @8e;
  }
  &-tag {
    background: rgba(207, 57, 48, 0.2);
    color: #cf3930;
  }
  .item {
    &-timeline {
      &-circle {
        background-color: @31;
        &::after {
          background-color: @66;
        }
      }
      &-tick {
        background-color: @31;
      }
    }
    &-body {
      &-time {
        color: @8e;
      }
      &-title {
        color: @nightfont;
        &-tag {
          background-color: @nightred;
          color: @ff;
        }
      }
      &-digest {
        color: @a9;
      }
      &-opt {
        &-right {
          color: #a9a9a9;
        }
        &-readIcon {
          background-image: url('../../assets/reading-number-dark.png');
        }
        &-shareIcon {
          background-image: url('../../assets/news-share-dark.png');
        }
      }
    }
  }
  .important {
    &-circle {
      background-color: #cf3930 !important;
    }
    &-font {
      color: @nightred;
    }
    &-font-p70 {
      color: @nightred-p70;
    }
    &-read {
      color: @8e!important;
    }
    &-read-red {
      color: @nightred-p70!important;
    }
  }
}

.show-item-enter-active {
  transition: all 0.8s ease;
}

.show-item-enter {
  transform: translateX(50px);
  opacity: 0;
}
</style>
