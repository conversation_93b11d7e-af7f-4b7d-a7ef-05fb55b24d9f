<template>
  <div>
    <snsPullDown
      noticeDirection="up"
      :notice="[{ Main: 'pulldown' }]"
      :option="{
        loadedStayTime: 1000,
        triggerDistance: 50,
        stayDistance: 40,
        damp: 3,
      }"
      :updateTip="tipTag"
      :updateNum="updateCount"
      :closePulldown="isIndex"
    >
      <div class="container Gfixed44">
        <!-- 二级导航 -->
        <snsScrollNav :tabListData="tabListData" @clickTab="handleClickTab"></snsScrollNav>

        <!-- 播放以及快讯提醒 -->
        <div ref="fixheader" v-fixed="{ class: 'fixed', top: 0, event: scrollTop }">
          <div class="header">
            <!-- <span class="header-time">{{dayTime}}</span> -->
            <div @click="handleClickVoice" style="display: flex; align-items: center">
              <span class="header-voiceImg"></span>
              <span class="header-voiceFont">播放全部</span>
            </div>
          </div>
        </div>

        <!-- 消息流 -->
        <newsFeed
          ref="newsFeed"
          :newsList="newsList"
          :scroll="scrollTop"
          :isLoading="isLoading"
          :curTab="currentTab"
          :nativeCb="nativeCb"
          :codeListValues="codeListValues"
          @load="handleScrollLoad"
          @setHeadTime="handleSetHeadTime"
        >
        </newsFeed>
      </div>
    </snsPullDown>

    <snsBackButton :updateCountNum="updateCountNum" :tabType="currentTab.type"> </snsBackButton>
  </div>
</template>

<script>
import getEnv from '@ths/getEnv';
import snsPullDown from '@/components/feature/snsPullDown';
import snsScrollNav from '@/components/feature/snsScrollNav';
import newsFeed from './newsFeed';
import fixed from '@/common/mixin/v-fixed.js';
import Emitter from '@/common/js/emitter.js';
import { sns } from '@/common/js/sns';
import { search } from '@/common/js/utils';
import $ from 'zepto';
import snsBackButton from './backToTopButton/snsBackButton';
import {
  getTabList,
  getPushList,
  getRefreshList,
  useNewTab,
  getNewTabList,
  getEtfTabList,
} from '@/common/api/index.js';
import { errorReport, warningReport } from '@thsf2e/useelk/lib/report.esm';
import * as HXKline from '@ths-m/HXKline';
const env = getEnv(navigator.userAgent);
const mapBlack = ['机会', '疫情'];

// tab枚举值
const TAB_ID_ENUM = {
  all: '全部',
  zy: '重要',
  ag: 'A股',
  gg: '港股',
  mg: '美股',
  jh: '机会',
  qh: '期货',
  yd: '异动',
  gg2: '公告',
};

export default {
  name: 'Main',
  mixins: [fixed, Emitter],
  components: {
    snsPullDown,
    newsFeed,
    snsScrollNav,
    snsBackButton,
  },
  data() {
    return {
      durationTime: 0,
      durationTimer: null,
      ajaxQueue: this.$sns.ajaxQueue,
      scrollTop: 0,
      tabListData: [],
      newsList: [], // 当前tab下的数据列表
      currentTab: {},
      updateCount: 0,
      isPlaying: false,
      isPause: false,
      isLoading: false,
      headerTime: 0,
      feedItemPositionList: [],
      curPlaySeq: '',
      shareTarget: '',
      nativeCb: null,
      reQueryTimer: null,
      tempNewsList: [], //自动更新时临时存放请求的数据
      tempList: [], // newsList的替身数组
      tipTag: true, //是否显示更新提示条数
      showTopNotice: false, //是否开启通知, true表示未开始
      pushInterfaceParams: {
        subId: '',
        subType: 100,
        subedId: 21001,
        subedType: 200,
        business: 'zixun_subscribe',
      },
      queryUrl: {},
      initImportantflag: true,
      isCurPageView: true,
      hxklineInstance: null,
      codeListValues: {},
    };
  },
  computed: {
    isIOS() {
      return getPlatform().indexOf('iphone') > -1;
    },
    dayTime() {
      let time = this.headerTime;
      if (String(this.headerTime).length === 10) {
        time = this.headerTime * 1000;
      }
      const year = new Date(time).getFullYear();
      const month = this.addTimeZero(new Date(time).getMonth() + 1);
      const day = this.addTimeZero(new Date(time).getDate());
      return `${year}年${month}月${day}日`;
    },
    isIndex() {
      return sns.getUrlParams('index') === '1';
    },
    updateCountNum() {
      return this.tempNewsList.length;
    },
  },
  async created() {
    window.weblog &&
      window.weblog.setConfig({
        appKey: 'ce19ea099b',
      });
    const { VUE_APP_HQ_ID, VUE_APP_HQ_PROJECT_KEY } = process.env;
    const config = {
      id: VUE_APP_HQ_ID,
      token: VUE_APP_HQ_PROJECT_KEY,
    };
    console.log(config);
    HXKline?.verifyProjectPermission(config);
    this.hxklineInstance = HXKline.initDataFeed({
      callback: (type, res) => {},
      //@ts-ignore
      codeList: [],
    });
    await this.initTab();
    await this.initData(); //初始化逻辑
    this.registEmitListener(); //注册事件监听器
    this.registNativeListener(); //注册客户端协议监听器
    this.showTopTip(); //展示顶部通知
    this.initTitle(); // 从底层页进入的时候显示“快讯”的title
    //轮询股票行情
    // this.$sns.intervalQueue.pushQueue(() => {
    //   // this.getMultimarketReal('all').then();
    // }, 1000 * 60);
    this.handleDuration();
    this.$sns.intervalQueue.pushHandle(() => {
      this.handleDuration();
    }, 'onShow');
    this.$sns.intervalQueue.pushHandle(() => {
      this.recordDuration();
    }, 'onHide');
    this.$sns.intervalQueue.initQueue();
  },
  mounted() {
    window.registerWebListener('handleRefresh', type => {
      // 回调参数type为['0']或['1']两种情况 ['0'] => 初始上拉  ['1'] => 主动点击刷新
      if (!type || type[0] === '0') {
        // 初始化上拉时,不展示下拉动画
        this.$sns.funcStat(`${this.$pid}`);
        this.broadcast('snsPullDown', 'loading', [false, true]);
      } else {
        //清空临时存放的更新数据
        this.tempNewsList = [];
        // 即type[0]==='1'时主动点击刷新,展示下拉动画
        window.scroll(0, 0);
        this.broadcast('snsPullDown', 'loading', true);
      }
    });
    // this.onShowPageChange(); // 监听页面回滚后退获取用户订阅的状态
    //监听滚动事件
    window.addEventListener('scroll', e => {
      this.scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
      const headPosition = document.getElementsByClassName('header')[0].getBoundingClientRect().top;
      if (headPosition !== 0 || this.scrollTop === 0) {
        this.newsList = [...this.tempNewsList, ...this.newsList];
        if (this.tempNewsList.length !== 0) {
          this.initTopTime(this.tempNewsList, this.currentTab.name);
        }
        this.tempNewsList = [];
        this.addTimeFlag(this.newsList);
      }
    });

    this.pageViewCycle();
  },
  methods: {
    // 页面生命周期处理
    pageViewCycle() {
      // alert('开始注册')
      const handle = this.isIOS ? window.registerWebHandler : window.registerWebListener;

      // 重新进入到页面
      handle('onShow', () => {
        // alert('开始轮训')
        if (this.showTopNotice) this.hasOpenNotify();
        this.$sns.intervalQueue.startQueue();
        this.$sns.intervalQueue.startShowQueue();
      });

      // 推出页面
      handle('onHide', () => {
        // alert('退出轮训')
        this.$sns.intervalQueue.stopQueue();
        this.$sns.intervalQueue.startHideQueue();
      });
    },

    handleDuration() {
      this.durationTimer && clearInterval(this.durationTimer);
      this.durationTime = 0;
      this.durationTimer = setInterval(() => {
        this.durationTime++;
      }, 1000);
    },

    recordDuration() {
      window.weblog &&
        window.weblog.report({
          id: 'ths_msg_information_kuaixun_List',
          action: 'stay',
          logmap: {
            stayTime: this.durationTime,
          },
        });
      this.durationTimer && clearInterval(this.durationTimer);
    },

    // 初始化tab栏，把“全部”的id存进去localstorage中，有缓存之后请求tab和list将同时请求
    async initTab() {
      if (this.tabListData.length === 0) {
        // 首次请求,tablist是要展示的所有
        try {
          // 检查enterSource参数
          const enterSource = sns.getUrlParams('enterSource');

          // 获取策略配置
          const strategies = this.getTabStrategies();

          // 根据enterSource选择对应的策略，如果没有对应策略则使用默认策略
          const strategy = strategies[enterSource] || strategies.default;

          // 执行选中的策略
          const res = await strategy();

          // 验证返回结果
          if (!res || !res.length) {
            throw new Error('Tab列表数据为空');
          }
          this.handleTabListData(res);
          localStorage.setItem('firstTabId', this.tabListData[0].id);
          localStorage.setItem('newsTab', JSON.stringify(this.tabListData));
          localStorage.setItem('tabListData', JSON.stringify(this.tabListData));
        } catch (err) {
          errorReport({ name: 'Tab列表出现问题', message: `错误${err}` });
          // 当接口请求失败的时候从localStorage缓存中去tab栏的数据
          if (localStorage.getItem('tabListData')) {
            this.handleTabListData(JSON.parse(localStorage.getItem('tabListData')));
          }
        }
        if (localStorage.getItem('firstTabId')) {
          await this.requestNewsListData({ seq: 0, tagId: localStorage.getItem('firstTabId') }, '2');
        }
      }
    },
    // 获取tab获取策略配置
    getTabStrategies() {
      return {
        // 默认策略：使用useNewTab判断
        default: async () => {
          const useNew = await useNewTab();
          let res = [];
          if (useNew) {
            res = await getNewTabList();
          } else {
            res = await getTabList();
          }
          return res.filter;
        },
        // ETF策略：调用ETF接口，失败时使用默认tab列表
        etf: async () => {
          try {
            const res = await getEtfTabList();
            const subKey = 'FlashNewsTab';
            const data = JSON.parse(res).find(item => item.key === subKey).tabConfig;
            if (!data || !data.length) {
              throw new Error('ETF接口返回数据为空');
            }
            return data;
          } catch (err) {
            console.error('ETF Tab列表获取失败', err);
            errorReport({ name: 'ETF Tab列表获取失败', message: `错误${err}` });
            const DEFAULT_ETF_TAB = [
              { id: 67339, name: '全部' },
              { id: 67327, name: '异动' },
              { id: 67329, name: '要闻' },
            ];
            return DEFAULT_ETF_TAB;
          }
        },
      };
    },
    // 获取枚举值的唯一code
    getTabType(item) {
      let type = item.id;
      Object.keys(TAB_ID_ENUM).map(t => {
        if (TAB_ID_ENUM[t] === item.name) {
          type = t;
        }
      });
      return type;
    },
    // 处理tab数据
    handleTabListData(data) {
      this.tabListData = data;
      this.tabListData.forEach((item, index) => {
        item.type = this.getTabType(item);
        item.firstid = '';
        if (index === 0) {
          this.currentTab = item;
          item.active = true;
        } else {
          item.active = false;
        }
      });
    },
    // 初始化数据，相隔10秒自动更新列表
    async initData() {
      this.headerTime = new Date().getTime();
      this.$sns.funcStat(`${this.$pid}`, '', { url: window.location.href });
      this.broadcast('snsPullDown', 'loading');
      //自动请求
      this.$sns.intervalQueue.pushQueue(async () => {
        this.getTemNewsList();
        let firstItem;
        if (this.tempNewsList.length) {
          firstItem = this.tempNewsList[0];
        } else {
          firstItem = this.tempList[0];
        }
        // 相隔10秒自动更新列表，确保是当前页面，才触发请求
        firstItem &&
          (await this.requestRefreshData({
            tagId: this.currentTab.id,
            createTime: firstItem.createTime,
            seq: firstItem.seq,
            envTag: 'reqfix',
          }));
      }, 10 * 1000);
    },
    // 切换tab栏，将会触发pulldown函数
    handleClickTab(val) {
      if (val.name === this.currentTab.name) {
        return;
      }
      this.currentTab = val;
      this.$sns.funcStat(`${this.$pid}.check.${val.type}`);
      this.tabListData.forEach(tab => {
        if (tab.id === val.id) {
          tab.active = true;
          const params = sns.getUrlParams('index') === '1' ? [false, true] : false;
          this.broadcast('snsPullDown', 'loading', params);
        } else {
          tab.active = false;
        }
      });
    },
    // 此方法在切换tab时会触发
    async pulldown(flag = true) {
      if (flag) {
        this.$sns.funcStat(`${this.$pid}.refresh`);
      }
      this.tabListData.forEach(tab => {
        if (tab.id === this.currentTab.id) {
          // 切换tab时先清空newsList，然后再调用接口重新赋值
          this.newsList = [];
          this.broadcast('snsScrollLoad', 'reset');
        }
      });
      const tabParams = {
        seq: 0,
        tagId: this.currentTab.id,
      };
      await this.requestNewsListData(tabParams, '3');
    },
    // 滚动到底部之后根据上一页最后一条数据的seq加载第二页的数据，每次加载20条数据
    async handleScrollLoad(page) {
      this.getTemNewsList();
      const lastItem = this.tempList[this.tempList.length - 1];
      const tabParams = {
        seq: lastItem.seq,
        tagId: this.currentTab.id,
      };
      await this.requestNewsListData(tabParams, '4');
      this.$sns.funcStat(`${this.$pid}.load.${page}`);
    },
    // 请求快讯列表, 首次进入，切换tab栏，滚动20条分页时触发
    async requestNewsListData(params, where) {
      let res = {};
      this.tipTag = true;
      this.isLoading = true;
      this.ajaxQueue.setFlag('requestNewsListData');
      const ownFlag = this.ajaxQueue.getFlag('requestNewsListData');

      try {
        res = await getPushList(params);
        if (res) {
          if (params.tagId.toString() === localStorage.getItem('firstTabId')) {
            localStorage.setItem('list', JSON.stringify(res));
          }
        } else {
          errorReport({
            name: '列表接口为空',
            message: `位置${where},当前tab:${JSON.stringify(this.currentTab)}`,
          });
          res = JSON.parse(localStorage.getItem('list'));
        }
      } catch (err) {
        errorReport({
          name: '列表接口失败',
          message: `位置${where},当前tab:${JSON.stringify(this.currentTab)},错误:${err}`,
        });
        if (localStorage.getItem('list')) {
          res = JSON.parse(localStorage.getItem('list'));
        }
      }

      const currentFlag = this.ajaxQueue.getFlag('requestNewsListData');
      if (ownFlag !== currentFlag) {
        return;
      }
      this.ajaxQueue.clearFlag('requestNewsListData');
      if (this.newsList.length === 0) {
        this.initTopTime(res.list, this.currentTab.name);
      }
      this.newsList = [...this.newsList, ...res.list];
      this.handleDataList(res.list);
    },
    // 请求自动更新的数据
    async requestRefreshData(params) {
      this.tipTag = false;
      this.isLoading = true;
      try {
        const res = await getRefreshList(params);
        const headPosition = document.getElementsByClassName('header')[0].getBoundingClientRect().top;
        if (this.scrollTop === 0 || headPosition !== 0) {
          // 如果页面在最上方，直接更新列表
          this.newsList = [...res.list, ...this.newsList];
          this.initTopTime(this.newsList, this.currentTab.name);
        } else if (this.scrollTop !== 0 && headPosition == 0) {
          // 如果页面已经滚动， 先把新更新的数据用一个数组存起来
          this.tempNewsList = [...res.list, ...this.tempNewsList];
        }
        this.handleDataList(res.list, true);
        if (res.list.length) {
          this.$sns.funcStat(`free_zixun_724.auto.${this.currentTab.type}`);
        }
      } catch (err) {
        warningReport({ name: '自动刷新失败', message: `错误${err}` });
      }
    },
    handleDataList(data, selfRefresh = false) {
      this.updateCount = 0;
      const dataList = this.handleForEach(data || []);
      this.getMultimarketReal(dataList);
      this.newsList.length === 0 && this.initTopTime(data, this.currentTab.name);
      this.addTimeFlag(this.newsList);
      if (!selfRefresh) {
        this.isLoading = false;
        this.broadcast('snsPullDown', 'loaded');
      }
    },
    handleForEach(dataList) {
      dataList.forEach(async x => {
        const arr = JSON.parse(JSON.stringify(x.stocks));
        const obj = x.fields ? x.fields[0] : false;
        if (obj) {
          const { name, fieldCode, fieldMarket } = obj;
          arr.unshift({
            name,
            stockCode: fieldCode,
            stockMarket: fieldMarket,
          });
        }
        x.stocks = arr;
        const { tempHeight, aspectRatio } = await this.handlePicUrl(x);
        this.$set(x, 'realImageHeight', tempHeight);
        this.$set(x, 'shareImageRatio', aspectRatio);
      });
      return dataList;
    },
    handlePicUrl(x) {
      return new Promise(resolve => {
        if (x.picUrl) {
          // 对图片的处理，在父组件处理，把处理好的值直接传给子组件
          const divHeight = 2.6; // 把图片作为div盒子的背景图片，div盒子的高度为2.6rem，即图片展示的最大高度
          const divWidth = 3.4; // div盒子的宽度，即图片展示的最大宽度，
          const radix = 0.02; // rem和px相互转化的基数
          const image = new Image();
          image.src = x.picUrl;
          image.onload = () => {
            let tempHeight = 2.6;
            const imageWidthRadix = image.width * radix;
            const imageHeightRadix = image.height * radix;
            const aspectRatio = imageHeightRadix / imageWidthRadix;

            //原图片宽高比例 大于 指定的宽高比例，这就说明了原图片的宽度必然 > 高度
            if (image.width / image.height >= divWidth / divHeight) {
              if (imageWidthRadix > divWidth) {
                // 按原图片的比例进行缩放
                tempHeight = (image.height * divWidth) / image.width;
              } else {
                // 按原图片的大小进行缩放
                tempHeight = imageHeightRadix;
              }
            } else {
              // 原图片的高度必然 > 宽度
              tempHeight = imageHeightRadix > divHeight ? divHeight : imageHeightRadix;
            }
            resolve({ tempHeight, aspectRatio });
          };
        }
      });
    },
    handleClickVoice() {
      // 调用语音播放的客户端协议，把20条快讯的列表数据传进voiceListPage协议
      this.$sns.funcStat('zixun_kuaixun.voicecast.start');
      const simplyList = [];
      for (const item of this.newsList) {
        simplyList.push({
          title: item.title,
          seq: item.seq,
          url: this.$sns.httpUrl(item.appUrl),
          source: item.source,
        });
      }
      window.callNativeHandler('voiceListPage', {
        key: 'playVoice',
        data: {
          list: simplyList,
          position: 0,
        },
      });
    },
    initTitle() {
      // 从底层页进入的时候显示“快讯”的title
      if (search.get('tag') === 'important') {
        if (!this.isIOS) {
          window.callNativeHandler(
            'changeWebViewTitle',
            {
              title: '快讯',
              url: '',
            },
            function () {}
          );
        } else {
          window.callNativeHandler('updateTitleAutomatically', '');
        }
        document.title = '快讯';
      }
    },
    hasOpenNotify() {
      //是否开启通知
      callNativeHandler('getPushNotifyState', '', ({ notifyState }) => {
        notifyState = +notifyState;
        this.showTopNotice = !notifyState;
      });
    },
    showTopTip() {
      this.hasOpenNotify();
      // console.log('isIOS?', this.isIOS ? '是' : '不是');
      // registerWebListener('onShow', () => {
      //   //当页面再次显示并且未开启通知的情况下再次检查是否已开启通知
      //   if (this.showTopNotice) this.hasOpenNotify();
      // });
    },
    closeTopTip() {
      this.showTopNotice = false;
      sns.funcStat('free_zixun_724.notification.close');
    },
    jumpToSetting() {
      sns.funcStat('free_zixun_724.notification.open');
      callNativeHandler('jumpAppSetting', '', () => {
        console.log('跳转至设置页2');
      });
    },
    openPlague() {
      this.$sns.funcStat('free_zixun_724.more.yq');
      const url =
        'client.html?action=ymtz^webid=2804^url=https://activity.10jqka.com.cn/acmake/cache/846.html^mode=new';
      location.href = url;
    },
    async registEmitListener() {
      this.$on('pulldown', await this.pulldown);
      this.$on('SET_HEAD_TIME', time => {
        this.headerTime = time;
      });
      // this.$on('emitPosition', this.handlePositionCallBack)
    },
    registNativeListener() {
      callNativeHandler('NotifyNativeEventToWeb', '');
      registerWebHandler('NotifyNativeEventToWeb', data => {
        const { type } = this.currentTab;
        if (data.key == 'share_1') {
          // 点击分享
          this.$sns.funcStat(`${this.$pid}.${type}.share.menu`);
        }
        if (data.key == 'share_2') {
          // 取消分享
          this.$sns.funcStat(`${this.$pid}.${type}.share.quxiao`);
        }
        if (data.key == 'share_3') {
          // 点击app
          this.$sns.funcStat(`${this.$pid}.${type}.share.${data.result['value_0']}`);
          this.shareTarget = data.result['value_0'];
        }
        if (data.key == 'share_4') {
          // 分享结果 code 0:成功 1:失败
          if (data.result && data.result.code == '0' && this.shareTarget) {
            this.$sns.funcStat(`${this.$pid}.${type}.share.${this.shareTarget}.receipt.succ`);
          } else if (data.result && data.result.code == '1') {
            this.$sns.funcStat(`${this.$pid}.${type}.share.${this.shareTarget}.receipt.fail`);
          }
        }
        // if (data.key == 'zx_tts_stop') {
        //     this.handleVoiceListener(data.result.code)
        // }
      });
      registerWebHandler('notifyWebHandleEvent', data => {
        this.nativeCb = data;
      });
      // registerWebHandler('zxVoiceBroadcast', (data) => {
      //     this.handleVoiceListener(data.state)
      // })
    },
    handleTest() {
      this.nativeCb = Math.random();
    },
    initTopTime(list, tab) {
      if (list.length === 0) {
        return;
      }
      this.headerTime = list[0].createTime;
      const history = JSON.parse(localStorage.getItem('newsTab'));
      let lastUpdate = '';
      history.forEach(h => {
        if (h.name === tab) {
          lastUpdate = h.firstid;
        }
      });
      if (lastUpdate) {
        if (list.filter(x => x.id === lastUpdate).length > 0) {
          list.forEach((item, index) => {
            if (item.id === lastUpdate) {
              this.updateCount = index;
            }
          });
        } else {
          this.updateCount = list.length;
        }
        history.forEach(item => {
          if (item.name === tab && list.length > 0) {
            item.firstid = list[0].id;
          }
        });
        localStorage.setItem('newsTab', JSON.stringify(history));
      } else {
        history.forEach(item => {
          if (item.name === tab && list.length > 0) {
            item.firstid = list[0].id;
          }
        });
        localStorage.setItem('newsTab', JSON.stringify(history));
        this.updateCount = list.length;
      }
    },
    addTimeFlag(list) {
      // 列表中出现两天或两天以上的数据，需要标记第一篇文章和最后一篇文章
      const date = Array.from(new Set(list.map(item => new Date(item.createTime * 1000).getDate())));
      if (date.length > 1) {
        const firstNewsArr = this.getFirstNewsOfDay(list);
        const lastNewsArr = this.getLastNewsOfDay(list);
        list.forEach(news => {
          news.isFirstNew = false;
          news.isLastNew = false;
        });
        list.forEach(news => {
          // 标记每天第一篇文章
          firstNewsArr.forEach(x => {
            if (news.id === x.id) {
              news.isFirstNew = true;
            }
          });
          // 标记每天最后一篇文章
          lastNewsArr.forEach(x => {
            if (news.id === x.id) {
              news.isLastNew = true;
            }
          });
        });
      }
    },

    getFirstNewsOfDay(arr) {
      const tempArr = [];
      arr.forEach((item, index) => {
        const day = new Date(item.createTime * 1000).getDate();
        if (arr[index + 1] && new Date(arr[index + 1].createTime * 1000).getDate() !== day) {
          tempArr.push(arr[index + 1]);
        }
      });
      return tempArr;
    },
    getLastNewsOfDay(arr) {
      const tempArr = [];
      arr.forEach((item, index) => {
        const day = new Date(item.createTime * 1000).getDate();
        if (arr[index + 1]) {
          if (new Date(arr[index + 1].createTime * 1000).getDate() !== day) {
            tempArr.push(arr[index]);
          }
        }
      });
      return tempArr;
    },
    // 时间补0
    addTimeZero(time) {
      if (time < 10) {
        return `0${time}`;
      } else {
        return time;
      }
    },
    // 获取股票涨跌幅
    async getMultimarketReal(list = []) {
      if (list.length > 0) {
        const codeList = [];
        const code_list = [];
        list.forEach(news => {
          news.stocks.forEach(x => {
            const existingMarket = code_list.find(r => r.market === String(x.stockMarket));
            if (existingMarket) {
              // 确保 stockCode 不重复
              if (!existingMarket.codes.includes(x.stockCode)) {
                existingMarket.codes.push(x.stockCode);
              }
            } else {
              code_list.push({ codes: [x.stockCode], market: String(x.stockMarket) });
            }
            if (!codeList.some(item => item.code === x.stockCode)) {
              codeList.push({
                id: 'flashnews',
                code: x.stockCode,
                market: String(x.stockMarket),
                data_class: 'snapshot',
                trade_class: 'intraday',
                msgCallback: (type, data) => {
                  console.log(type, data, '订阅回调');
                  if (type === 'message') {
                    console.log(this.codeListValues, 'codeListValues');
                    this.$set(
                      this.codeListValues,
                      x.stockCode,
                      data.zhangdiefu != null && String(data.zhangdiefu)
                    );
                  }
                },
              });
            }
          });
        });

        if (code_list.length) {
          // 首次请求需要根据股票获取到快照数据(收盘的原因)
          const codeLists = {
            code_list: code_list,
            data_fields: ['199112'],
            trade_class: 'intraday',
            gpid: 1,
          };
          try {
            const dataSnapshot = await this.hxklineInstance.getSnapshotData(codeLists);
            dataSnapshot.data.forEach(item => {
              this.$set(
                this.codeListValues,
                item.code,
                item.format_data[0].zhangdiefu != null && String(item.format_data[0].zhangdiefu)
              );
            });
            console.log(dataSnapshot, this.codeListValues, '快照');
          } catch (err) {
            console.log(err);
          }
        }
        console.log('codeList', codeList, code_list);
        if (codeList.length) {
          this.hxklineInstance.subscribeStocks(codeList);
        }
      }
    },
    // 从快讯列表中获取股票并去重后拼凑请求参数
    getStocksParams(list) {
      const hash = {};
      const marketHash = {};
      const arr = list
        .map(x => x.stocks)
        .reduce((item, cur) => {
          item = [...item, ...cur];
          return item;
        }, [])
        .reduce((item, cur) => {
          if (!hash[cur.stockCode]) {
            item.push(cur);
            hash[cur.stockCode] = true;
          }
          return item;
        }, []);
      const market = arr.reduce((item, cur) => {
        if (!marketHash[cur.stockMarket]) {
          item.push(cur.stockMarket);
          marketHash[cur.stockMarket] = true;
        }
        return item;
      }, []);
      const stockCode = [];
      market.forEach(item => {
        stockCode.push([]);
      });
      market.forEach((item, index) => {
        arr.forEach(x => {
          if (x.stockMarket === item) {
            stockCode[index].push(x.stockCode);
          }
        });
      });
      const code = stockCode.map(x => x.join('_'));
      return `${market.join(',')}/${code.join(',')}`;
    },
    refresh() {
      window.location.reload();
    },
    getTemNewsList() {
      if (this.tabListData.length > 0) {
        this.tempList = this.newsList;
      } else {
        this.tempList = [];
      }
    },
  },
};
</script>

<style lang="less" scoped>
.msgtip {
  display: flex;
  width: 92%;
  margin: 0 auto;

  .text {
    flex: 1;
    text-align: center;
  }
}

.container {
  height: 100%;
  background-color: @ff;
}

.tab {
  display: flex;
  flex-wrap: nowrap;
  overflow-x: scroll;
  -webkit-overflow-scrolling: touch;
  padding: 0.3rem 0.32rem 0.2rem 0;

  &::-webkit-scrollbar {
    display: none;
  }

  &-pane {
    height: 0.56rem;
    flex-shrink: 0;
    padding-left: 0.32rem;

    &:last-child {
      padding-right: 0.32rem;
    }

    &-block {
      padding: 0 0.2rem;
      height: 0.56rem;
      font-size: 0.28rem;
      text-align: center;
      line-height: 0.56rem;
      color: @66;
      letter-spacing: 0;
      border-radius: 0.04rem;
      background-color: @f5;

      &-active {
        background-color: @red;
        color: @ff;
      }
    }
  }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.16rem 0.32rem;
  background-color: @ff;

  &-time {
    font-size: 0.28rem;
    color: @99;
    letter-spacing: 0;
  }
  &-voiceImg {
    display: inline-block;
    background-image: url('../assets/voice.png');
    background-size: 100% 100%;
    height: 0.38rem;
    width: 0.38rem;
  }
  &-voiceFont {
    font-size: 0.28rem;
    color: @66;
    letter-spacing: 0;
    margin-left: 0.15rem;
  }
  &-font {
    font-size: 0.28rem;
    color: @66;
    letter-spacing: 0;
    margin-right: 0.15rem;
  }

  &-btn {
    width: 1.18rem;
    height: 0.48rem;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 0.02rem solid @red;
    background-color: @red-p5;
    color: @red;
    font-size: 0.24rem;
    line-height: 0.24rem;
    border-radius: 0.04rem;

    img {
      width: 0.18rem;
      height: 0.24rem;
      margin-right: 0.12rem;
    }
  }
}

.fixed {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: @ff;
  z-index: 1000;
}

.night {
  .container {
    background-color: #1c1c1c !important;
  }

  .tab {
    &-pane {
      &-block {
        color: @nightfont;
        background-color: @32;

        &-active {
          background-color: @nightred;
          color: @ff;
        }
      }
    }
  }

  .header {
    background-color: #1c1c1c !important;

    &-time {
      color: @8e;
    }

    &-font {
      color: #a9a9a9;
    }

    &-voiceImg {
      background-image: url('../assets/voice-night.png');
    }
    &-voiceFont {
      color: @a9;
    }

    &-btn {
      border: 0.02rem solid @nightred;
      background-color: @nightred-p5;
      color: @nightred;
    }
  }

  .fixed {
    background-color: #1c1c1c;
  }
}
</style>
