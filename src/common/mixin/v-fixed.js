const fixed = {
  directives: {
    fixed: {
      // 用法:
      // 传入一个类名和滚动事件（或距离顶部高度），指令将在dom节点接触到顶部时给子元素加上传入的类名，并动态给绑定指令的dom设置高度，避免高度塌陷
      // top是触发距离如果不想吸顶在顶部可以提前触发
      // <div v-fixed="{class: '触发吸顶时给子元素加的类名', top: 0, event: 滚动事件回调}">
      //   <div>需要fixed的元素</div>
      // </div>
      update(el, binding) {
        const fixexTop = binding.value.top || 0;
        const {top} = el.getBoundingClientRect();
        const {height} = el.children[0].getBoundingClientRect();
        let hasClass = false;
        for(let i = 0; i < el.children[0].classList.length; i++) {
          if (el.children[0].classList[i] === binding.value.class) {
            hasClass = true;
          }
        }
        if (top >= fixexTop && hasClass) {
          el.children[0].classList.remove(binding.value.class);
          el.style.height = 'auto';
        } else if (top < fixexTop && !hasClass) {
          el.children[0].classList.add(binding.value.class);
          el.style.height = `${height}px`;
        }
      }
    }
  }
};

export default fixed;
