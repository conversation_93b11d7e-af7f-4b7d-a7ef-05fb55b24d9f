/* eslint-disable */
const sns = {};

/**
 *
 * @method unparam
 * @see  {@link }
 * @description  Parses a URI-like query string and returns an object composed of parameter/value pairs.
 *
 * for example:
 *      @example
 *      FSJ.unparam('section=blog&id=45')       // -> {section: 'blog', id: '45'}
 *      FSJ.unparam('section=blog&tag=js&tag=doc') // -> {section: 'blog', tag: ['js', 'doc']}
 *      FSJ.unparam('tag=ruby%20on%20rails')       // -> {tag: 'ruby on rails'}
 *      FSJ.unparam('id=45&raw')       // -> {id: '45', raw: ''}
 * @param {String} str param string
 * @param {String} [sep='&'] separator between each pair of data
 * @param {String} [eq='='] separator between key and value of data
 * @return {Object} json data
 * <AUTHOR> add at 2016-09-21
 */
function unparam(str, sep, eq) {
  var SEP = '&';
  var EQ = '=';
  function urlDecode(s){
    return decodeURIComponent(s.replace(/\+/g, ' '));
  }
  /**
   * test whether a string end with a specified substring
   * @param {String} str the whole string
   * @param {String} suffix a specified substring
   * @return {Boolean} whether str end with suffix
   */
  function endsWith(str, suffix) {
    var ind = str.length - suffix.length;
    return ind >= 0 && str.indexOf(suffix, ind) === ind;
  }

  if (typeof str !== 'string' || !(str = $.trim(str))) {
    return {};
  }
  sep = sep || SEP;
  eq = eq || EQ;
  var ret = {},
    eqIndex,
    decode = urlDecode,
    pairs = str.split(sep),
    key, val,
    i = 0, len = pairs.length;

  for (; i < len; ++i) {
    eqIndex = pairs[i].indexOf(eq);
    if (eqIndex === -1) {
      key = decode(pairs[i]);
      val = '';
      //val = undefined;
    } else {
      // remember to decode key!
      key = decode(pairs[i].substring(0, eqIndex));
      val = pairs[i].substring(eqIndex + 1);
      try {
        val = decode(val);
      } catch (e) {
        //logger.error('decodeURIComponent error : ' + val);
        //logger.error(e);
      }
      if ( endsWith(key, '[]') ) {
        key = key.substring(0, key.length - 2);
      }
    }
    if (key in ret) {
      if ($.isArray(ret[key])) {
        ret[key].push(val);
      } else {
        ret[key] = [ret[key], val];
      }
    } else {
      ret[key] = val;
    }
  }
  return ret;
}

sns.unparam = unparam;

function getUrlParams (key) {
  /**
   * 获取url中参数
   * @param {string} key
   */
  if (!location.search) return false
  let searchStr = location.search
  if (searchStr[0] === '?') {
    searchStr = searchStr.substring(1);
  }
  if (searchStr.match('&')) {
    let resObj = {}
    searchStr.split('&').map((item) => {
      resObj[item.split('=')[0]] = item.split('=')[1]
    })
    return resObj[key]
  } else {
    return key === searchStr.split('=')[0] ? searchStr.split('=')[1] : false
  }
}
sns.getUrlParams = getUrlParams;

function initFontSize(){
  // if(flag){
  //   var viewPort = document.querySelector('meta[name="viewport"]');
  //   viewPort.setAttribute('content', 'user-scalable=0');
  // }
  // flag && metaSet(flag);
  metaSet();
  var sizeRate = document.documentElement.clientWidth/375.0*100;
  document.getElementsByTagName('html')[0].style.fontSize = sizeRate+'px';
  document.getElementsByTagName('html')[0].classList.add('fontinit');
  return sizeRate;
}
sns.initFontSize = initFontSize;

function metaSet(){
  var rem = {
    designWidth : 750, //设计稿宽px值
    px2rem : 100, //px to rem为100倍比例转换，宽度建议用百分比
    defaultFontSize : 20,//默认1rem字体大小
    maxWidth : 0,//rem适配最大尺寸(屏幕再大也就那个尺寸了)
    dpr : parseInt( window.devicePixelRatio || 1 ),
    foldingLimit: 480,
    foldingMaxWidth: 375,
  };

  try{
    if ( getPlatform() == 'gphone' ) {
      //手炒、股市教练android设置dpr不生效
      rem.dpr = 1;
      //直接禁用字体大小设置
      // callNativeHandler(
      // 	'webViewFontController',
      // 	'{"fontsize":"1", "switch":"0"}',
      // 	function(data) {}
      // );
    }
    // 适配折叠屏：如果屏宽大于480 则maxWidth改为375适配ui规范
    if (document.documentElement.clientWidth > rem.foldingLimit) {
      rem.maxWidth = rem.foldingMaxWidth;
    }
  }
  catch(e){
  }

  //根据devicePixelRatio自定计算scale
  //可以有效解决移动端1px这个世纪难题。
  var remEl = document.querySelector('meta[name="rem"]');

  //允许通过自定义name为rem的meta头，通过initial-dpr来强制定义页面缩放
  if (remEl) {
    var remCon = remEl.getAttribute('content');
    if (remCon) {
      var initialDprMatch = remCon.match(/initial\-dpr=([\d\.]+)/);
      if (initialDprMatch) {
        rem.dpr = parseFloat(initialDprMatch[1]);
      }
      var designWidthMatch = remCon.match(/design\-width=([\d\.]+)/);
      if (designWidthMatch) {
        rem.designWidth = parseFloat(designWidthMatch[1]);
      }

      var maxWidthMatch = remCon.match(/max\-width=([\d\.]+)/);
      if (maxWidthMatch) {
        rem.maxWidth = parseFloat(maxWidthMatch[1]);
      }
    }
  }

  document.documentElement.setAttribute('data-dpr', rem.dpr);
  document.documentElement.setAttribute('max-width', rem.maxWidth);


  var viewportEl = document.querySelector('meta[name="viewport"]');
  var scale = 1 / rem.dpr ;
  var content = 'width=device-width, initial-scale=' + scale + ', minimum-scale=' + scale + ', maximum-scale=' + scale + ', user-scalable=no';
  //ifind中需要平铺铺满适配iphoneX
  if(getApp() === 'ifind'){
    content += ',viewport-fit=cover';
  }
  if (viewportEl) {
    viewportEl.setAttribute('content', content);
  } else {
    viewportEl = document.createElement('meta');
    viewportEl.setAttribute('name', 'viewport');
    viewportEl.setAttribute('content', content);
    document.head.appendChild(viewportEl);
  }

  var d = document.createElement('div');
  d.style.width = '1rem';
  d.style.display = 'none';
  document.head.appendChild(d);
  try{
    rem.defaultFontSize = parseFloat(window.getComputedStyle(d, null).getPropertyValue('width'));
  }
  catch(e){
  }

  rem.get_px = function(px_num){
    var px = parseInt(px_num);
    if( !px ){
      return false;
    }
    return rem.dpr*px + 'px';
  }

  rem.resize = function(){
    //对，这个就是核心方法了，给HTML设置font-size。
    var measureWidth = 0;
    try{
      measureWidth = document.documentElement.getBoundingClientRect().width;
    }
    catch(e){
    }

    if( !measureWidth ){
      measureWidth = window.innerWidth;
    }

    if( !measureWidth ){ return false;}

    //设置最大尺寸
    if( rem.maxWidth ){
      var tp = rem.maxWidth*rem.dpr;
      if( measureWidth > tp ){
        measureWidth = tp;
      }
    }

    var fontSize = measureWidth / (rem.designWidth / rem.px2rem) / rem.defaultFontSize * 100 ;// * 2 因为我们这边切图都取一半
    var elName = 'rem';
    var remStyle = document.getElementById(elName);
    if( remStyle ){
      remStyle.innerHTML = "html{font-size:" + fontSize + "% !important;}";

    }
    else{
      remStyle = document.createElement('style');
      remStyle.setAttribute('id', elName);
      remStyle.innerHTML = "html{font-size:" + fontSize + "% !important;}";
      document.head.appendChild(remStyle);

    }

    rem.callback && rem.callback();

  };

  // if(parseInt(flag) > 1){
    rem.resize();
    // 直接调用一次

    window.addEventListener( 'resize' , function(){
      clearTimeout( rem.tid );
      rem.tid = setTimeout( rem.resize , 33 );
    } , false );
    // 绑定resize的时候调用

    window.addEventListener( 'load' , rem.resize , false );
    // 防止不明原因的bug。load之后再调用一次。

    setTimeout(function(){
      rem.resize();
      //防止某些机型怪异现象，异步再调用一次
    }, 333);
  // }
  // rem.resize();
  //直接调用一次

  // window.addEventListener( 'resize' , function(){
  // 	clearTimeout( rem.tid );
  // 	rem.tid = setTimeout( rem.resize , 33 );
  // } , false );
  // //绑定resize的时候调用

  // window.addEventListener( 'load' , rem.resize , false );
  // //防止不明原因的bug。load之后再调用一次。

  // setTimeout(function(){
  // 	rem.resize();
  // 	//防止某些机型怪异现象，异步再调用一次
  // }, 333);
}
sns.metaSet = metaSet;

//https url http过滤为对应 url
function httpUrl(url){
  url = $.trim(url);
  var reg = /^(http:|https:)/;

  if(typeof url == 'string' && url.length){
    if(url.indexOf('javascript') > -1){
      return url;
    }

    if(reg.test(url)){
      url = url.replace(reg,window.location.protocol);
    }else{
      url = window.location.protocol + url;
    }
  }
  return url;
}
sns.httpUrl = httpUrl;

function confirmBox(option){
  var defaultObj = {
    wrapClass:'',
    msg:'',
    leftBtn:'取消',
    rightBtn:'确定',
    btnLight:'none',//btnLight:left,right,none;点亮按钮
    lightColor:'#4691ee',
    leftBtnEvent:function(){},
    rightBtnEvent:function(){}
    // fontSizeUnit:'.16rem'//font用rem 用于有initFontSize的rem页面，传.16em；font用px 就传16px
  };

  var param = $.extend({},defaultObj,option);
  var strArr = [
    '<div class="confirm '+param.wrapClass+'">',
    '<div class="confirm-wrap">',
    '<div class="confirm-content">',param.msg,'</div>',
    '<div class="confirm-left">',param.leftBtn,'</div>',
    '<div class="confirm-right">',param.rightBtn,'</div>',
    '</div>',
    '</div>'
  ];

  if( $('.confirm').length <= 0 ){
    $(strArr.join('')).appendTo('body');
  }
  $('.confirm').show();
  if($('html').hasClass('fontinit')){
    $('.confirm').css('font-size','.32rem');
  }else{
    $('.confirm').css('font-size','32px');
  }
  //点亮按钮
  switch(param.btnLight){
    case 'right':
      $('.confirm-right').css('color',param.lightColor);
      break;
    case 'left':
      $('.confirm-left').css('color',param.lightColor);
      break;
    case 'none':
      $('.confirm-right,.confirm-left').css('color','#777');
      break;
    default:
      break;
  }

  var h = -$('.confirm').height()/2;
  $('.confirm').css('marginTop',h);
  sns.showMask();

  $('.confirm .confirm-left').off().click(function(event) {
    param.leftBtnEvent && param.leftBtnEvent();
    $('.confirm').remove();
    sns.hideMask();
  });

  $('.confirm .confirm-right').off().click(function(event) {
    param.rightBtnEvent && param.rightBtnEvent();
    $('.confirm').remove();
    sns.hideMask();
  });
}
sns.confirmBox = confirmBox;

function alertBox(msg,time){
  if($('.alertbox').length > 0){
    return;
  }

  $('<div class="alertbox"><span class="alertboxmsg">'+msg+'</span><div class="alertboxbg"></div></div>').appendTo('body');

  if($('html').hasClass('fontinit')){
    $('.alertbox').css({
      'font-size':'0.32rem'
    });
  }else{
    $('.alertbox').css('font-size','32px');
  }
  var h = $('.alertbox').height()/2;
  var w = $('.alertbox').width()/2;

  $('.alertbox').css({
    'top':'50%',
    'left':'50%',
    'margin-top':-h,
    'margin-left':-w
  });



  var stime = time || 2000;

  window.setTimeout(function(){
    if($('.alertbox').length){
      $('.alertbox').remove();
    }
  },stime);
}
sns.alertBox = alertBox;

function showMask(){
  if( !$('.pop_mask1').length ){
    $('<div class="pop_mask1"></div>').appendTo('body');
  }
  $('.pop_mask1').css('height',$(window).height());
  // $('body,html').css('height','100%');
  $('.pop_mask1').show();
}
sns.showMask = showMask;

function hideMask(){
  if( $('.pop_mask1').length ){
    $('.pop_mask1').hide();
  }
  // $('body,html').css('height','auto');
}
sns.hideMask = hideMask;

/**
 * 判断是否登录
 */
function isLogin(){
    // return true
  return getAccount() ? true : false;
}
sns.isLogin = isLogin;

/**
 * 公用doLogin
 * @return {[type]} [description]
 * type 是暂时的方案
 */
function doLogin(jumpToUrl,type){
	var platform = getApp();
	var loginNativeArr = ['gsjl','tzzb'];
	var url = jumpToUrl || window.location.href;
    if( loginNativeArr.indexOf(platform) >= 0 ){
    	callNativeHandler('login','',function(data){});
    }else if( platform == 'ths' || platform == 'liejin' || platform == 'ifind' || platform == "futures" || platform == 'iJiJin'){
    	window.location.href = 'http://eqhexin/changeUser';
    }else{
    	if(platform == 'weixin'){
			try{
                if(type && type === 'ask'){
                    wechatLogin(encodeURIComponent(url),'JRFW'); //问答需要统一加十&金融服务等公众号
                }else{
                    wechatLogin(encodeURIComponent(url));
                }
			}catch(e){
				//在微信或是其它浏览器中
				window.location.href = '//upass.10jqka.com.cn/login?platform=phone&view=ifind&redir='+encodeURIComponent(url);
			}
		}else{
			//在微信或是其它浏览器中
			window.location.href = '//upass.10jqka.com.cn/login?platform=phone&view=ifind&redir='+encodeURIComponent(url);
		}
    }
}
sns.doLogin = doLogin;

/*
 * 判断平台：股市教练、同花顺、其它浏览器
 */
function getApp(){
  var u = navigator.userAgent;
  var gsjlIphoneIndex = u.indexOf('IHexin_gsjl');
  var txhIphoneIndex = u.indexOf('IHexin/');
  var gphoneIndex = u.indexOf('Hexin_Gphone');
  var tzzbIphoneIndex = u.indexOf('IHexin_xcs');
  var tzzbGphoneIndex = u.indexOf('GHexin_xcs');
  var ljIndex = u.indexOf('Hexin_Metal');
  var wexin = u.toLowerCase().indexOf('micromessenger');
  var ifindIphoneIndex = u.indexOf('ifind_iphone');
  var ifindGphoneIndex = u.indexOf('ifind_gphone');
  var futuresIphoneIndex = u.indexOf('IHexin_Futures');
  var futuresGphoneIndex = u.indexOf('Hexin_Futures');
  let iJijinGphoneIndex = u.indexOf('GphoneIjiJin/V');
  let iJijinIphoneIndex = u.indexOf('IphoneIJiJinAPP/');

  var version = 1;
    if ( gphoneIndex > -1 ){
      version = parseInt(u.match(/Hexin_Gphone\/(.*)/)[1].split('.')[0]);
    }

    var app;
    //目前股市教练版本是1.X.X
    //同花顺app版本是9.X.X
    if( gsjlIphoneIndex > -1 || (gphoneIndex > -1 && version < 7) ){
      //在股市教练APP中
      app = 'gsjl';
    }else if( tzzbIphoneIndex > -1 || tzzbGphoneIndex > -1 ){
      //在投资账本APP中
      app = 'tzzb';
    }else if( ljIndex > -1 ){
      //猎金App中
      app = 'liejin';
    }else if( txhIphoneIndex > -1 || (gphoneIndex > -1 && version > 7) ){
      //在同花顺APP中
      app = 'ths';
    }else if( wexin > -1 ){
      //微信中
      app = 'weixin';
    }else if(ifindIphoneIndex > -1 || ifindGphoneIndex > -1){
      //ifind
      app = 'ifind';
    }else if(iJijinGphoneIndex > -1 || iJijinIphoneIndex > -1){
      //ai ji jin
      app = 'iJiJin';
    }else if (futuresIphoneIndex > -1 || futuresGphoneIndex > -1){
      app = 'futures';
    }else{
    app = 'other';
  }

    return app;
}
sns.getApp = getApp;

function urlPrefix(url) {
  var prefix = '//t.10jqka.com.cn';

  if (window.location.href.indexOf('t.ths123.com') !== -1) {
    prefix = '//t.ths123.com';
  }

  return /^[/]/.test(url) ? `${prefix}${url}` : `${prefix}/${url}`;
}
sns.urlPrefix = urlPrefix;

function strToObject(str){
    var args = {};

    if(typeof str == "undefined" ||  str.indexOf('=') == -1 ){
        return args;
    }
    var pairs = str.split("&");
    for(var i = 0, len = pairs.length; i < len; i++){
        var pos = pairs[i].indexOf('=');
        var argsName = pairs[i].substring(0, pos);
        var value = pairs[i].substring(pos + 1);
        args[argsName] = value;
    }
    return args;
}
sns.strToObject = strToObject;

/**
 * <AUTHOR>
 * @date 20170315
 * @desc 手炒图片放大功能
 */
function imageScale(option){
  //option参数：
  //box：对该容器内的图片进行放大功能
  //img：所需要放大的图片
  //stat：统计代码
  //btn：启动放大的按钮，需要在按钮上加入src属性（可不传，默认点击图片本身放大）
  var param = {
    box:'.wap-container',
    btn:'',
    img:'.img',
    stat:'',
    callback:function(){}
  };

  var opt = $.extend({},param,option);

  var start = opt.btn || opt.img;
  $(opt.box).on('click',start,function(event){
      var originImagesDom = $(opt.img);
        var detailImages = [];
        var originImages = [];
        var index = 0;
        var $this = $(this);
        var reg = /^(http:|https:)/;

        //如果点击的是图片，且图片的父层是a标签，且a标签的href是地址,则不放大图片
        //liukang
        var thisDom = this.tagName.toLowerCase();
        var $parentDom = $this.parents('a');

        if( thisDom === 'img' ){
          if( $parentDom.length > 0 && reg.test($parentDom.attr('href')) ){
            return true;
          }
        }

        $.each(originImagesDom, function(key,value){
          var imgUrl = $(value).attr('src');
            if(reg.test(imgUrl)){
                imgUrl = imgUrl.replace(reg,window.location.protocol);
            }else{
                imgUrl = window.location.protocol + imgUrl;
            }

            originImages.push(imgUrl);
            detailImages.push((imgUrl).replace(/(_small|_middle)/g, ''));

            if($(value).attr('src') == $this.attr('src')){
                index = key;
            }
        });
        if(getApp() == 'gsjl' || getApp() == 'ths' || getApp() == 'liejin' || getApp() == 'tzzb'){
          callNativeHandler(
              'displayImageThumbnail',
              {
                  'currentIndex':index,// 当前点击图片的index
                  'originImages':originImages,// 所有图片的缩略图的url，数组,
                  'detailImages':detailImages // 所有图片的高清图的url，数组
              },
              function(data){
                  console.log(data);
              }
          );
      }else{
        $.isFunction(opt.callback) && opt.callback(index,detailImages);
      }

        if(opt.stat !== ''){
          funcStat(opt.stat);
        }
    });
}
sns.imageScale = imageScale;

/*兼容回流v2版本的手抄外部页面埋点*/
function idStat(id,fid,param,type){
  var paramlist = {
    ts : Math.floor(+new Date()/1000)
  };
  if( !id ){
    id = '0';
  }
  if( fid ){
    paramlist['fid'] = fid;
  }
  paramlist['id'] = id;
  paramlist['ld'] = 'mobile';
  paramlist['platform'] = getPlatform();
  paramlist['client_userid'] = getUserid();

  if( param ){
    if( typeof param !== 'object' ){
      console.log('[input error]idStat param is not an object!');
      return false;
    }

    for(var i in param){
      paramlist[i] = param[i];
    }
  }

  switch (type){
    case 'ta' :
      var img = new Image();
      img.src = '//stat.10jqka.com.cn/q?'+http_build_query(paramlist);
      break;
    default :
      //如果不是10jqka.com.cn域名 第一次需要调用//stat.10jqka.com.cn/q?
      notifyClient('idcount', http_build_query(paramlist));
  }
  return true;
}
sns.idStat = idStat;

/**统计方法**/
/**
 * 进入页面的统计代码
 */
function pageStat(stat,fid,extObj){
  var id = fid || '';

  var param = {
    'id':stat,
    'fid':id
  };

  if(typeof extObj === 'object'){
    for(var i in extObj){
      param[i] = extObj[i];
    }
  }else{
    extObj = {};
  }

  if(typeof window.Gurlver !== 'undefined'){
    extObj['url_ver'] = window.Gurlver;
    param['url_ver'] = window.Gurlver;
  }

  if( getApp() === 'ths' ){
        hxmPageStat(param);
    }else{
        idStat(stat,id,extObj,'ta');
    }
}
sns.pageStat = pageStat;

/**
 * 事件触发的统计代码
 */
function funcStat(stat,fid,extObj){
  var id = fid || '';
	// 用于区分进入入口
	var enterSource = sns.getUrlParams('enterSource') || '';

  var param = {
    'fid':id,
		'enterSource': enterSource
  };

  if(typeof extObj === 'object'){
    for(var i in extObj){
      param[i] = extObj[i];
    }
  }else{
    extObj = {};
  }

  if(typeof window.Gurlver !== 'undefined'){
    extObj['url_ver'] = window.Gurlver;
    param['url_ver'] = window.Gurlver;
  }

  if( getApp() === 'ths' ){
        hxmClickStat(stat,param);
    }else{
        idStat(stat,id,extObj,'ta');
    }
}
sns.funcStat = funcStat;

/**
 * 页面跳转的统计代码
 * toRid 目标页面的id
 */

function jumpStat(stat,toRid,fid,extObj){
  var id = fid || '';
	// 用于区分进入入口
	var enterSource = sns.getUrlParams('enterSource') || '';

  var param = {
    'fid':id,
		'enterSource': enterSource
  };

  if(typeof extObj === 'object'){
    for(var i in extObj){
      param[i] = extObj[i];
    }
  }else{
    extObj = {};
  }

  if(typeof window.Gurlver !== 'undefined'){
    extObj['url_ver'] = window.Gurlver;
    param['url_ver'] = window.Gurlver;
  }

    if( getApp() === 'ths' ){
        hxmJumpPageStat(stat,toRid,param);
    }else{
        idStat(stat,id,extObj,'ta');
    }
}
sns.jumpStat = jumpStat;

/**
 * 通知客户端发送统计代码
 * @param  {[type]} params [description]
 * @return {[type]}        [description]
 * {
      method : stat
      params:{
             type：1，      //数字型，不能为空，需要和action_type 搭配使用 默认使用1
             object：xxx,    //字符型，不能为空，埋点的object，客户端会取字符串第一个“.”后面的部分
             to_resourcesid：xxx，  //字符型，可以为空，目标网页id
             to_frameid：2804，   //数字型，可以为空，目标frameid
             to_scode：300033，     //字符型，可以为空，目标股票代码
             targid ：xxx    //字符型，可以为空，拓展参数
             action_type ：0   //数字型，可以为空，埋点的actionType，0：NoneAction，1：ClickAction，2：ScreenAction，3：OtherAction，只有当type为1时该字段才生效。（详情见SJCGAMIPHONE-14240）

      }
  }

  一般只需要传递参数object
 */
function thsStat(stat, params){
  var opts = {
      method : 'stat',
      params:{
           type: 1,
           object: '',
           to_resourcesid: '',
           to_frameid: 2804,
           to_scode: '',
           targid: '',
           action_type: 1
      }
  };

  opts.params.object = stat;
  if(typeof params != 'undefined'){
    $.extend(true, opts, params);
  }

  callNativeHandler(
      'notifyWebHandleEvent',
      JSON.stringify(opts)
  );
}
sns.thsStat = thsStat;

/**
 * 动态ios的title处理方案 ios的title只会在初始化的时候获取一次，动态改变失效
 * 安卓也存在兼容问题 ，
 * @return {[type]} [description]
 */
function autoTitleForIOS (title) {
  // if(getPlatform() == 'iphone'){
    document.getElementsByTagName('title')[0].text = title;
    document.title = title;
    var $iframe = $('<iframe src="/favicon.ico"></iframe>');
    $iframe.on('load', function(){
      setTimeout(function(){
        $iframe.off('load').remove();
      },0);
    }).appendTo($('body'));
  // }
}
sns.autoTitleForIOS = autoTitleForIOS;

/**
 * 移动端跳404页面
 */
function handler404 () {
  window.location.href = '//t.10jqka.com.cn/app/404.html';
}
sns.handler404 = handler404;

/**
 *微信分享
 *微信开发文档 //mp.weixin.qq.com/wiki/home/<USER>
 */
/*exported isWeixin,citeWxJs*/

var wxShare = {
  init: function(){
    window.GAppid = '';
    window.Gtimestamp = '';
    window.GnonceStr = '';
    window.Gsignature = '';
  },

  share: function(shareInfo){
    wx.config({
      debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
      appId: window.GAppid, // 必填，公众号的唯一标识
      timestamp: window.Gtimestamp, // 必填，生成签名的时间戳
      nonceStr: window.GnonceStr, // 必填，生成签名的随机串
      signature: window.Gsignature,// 必填，签名，见附录1
      jsApiList: ['checkJsApi','onMenuShareTimeline','onMenuShareAppMessage','onMenuShareQQ','onMenuShareWeibo',
        'onMenuShareQZone','hideMenuItems','showMenuItems','hideAllNonBaseMenuItem','showAllNonBaseMenuItem',
        'translateVoice','startRecord','stopRecord','onVoiceRecordEnd','playVoice','onVoicePlayEnd','pauseVoice',
        'stopVoice','uploadVoice','downloadVoice','chooseImage','previewImage','uploadImage', 'downloadImage',
        'getNetworkType', 'openLocation','getLocation','hideOptionMenu','showOptionMenu','closeWindow',
        'scanQRCode','chooseWXPay','openProductSpecificView','addCard','chooseCard','openCard'] // 必填，需要使用的JS接口列表，所有JS接口列表见附录2
    });

    wx.ready(function(){
      wx.onMenuShareTimeline({
        title: shareInfo.shareTitle, // 分享标题
        link: shareInfo.shareLink, // 分享链接
        desc: shareInfo.shareDesc, //分享描述
        imgUrl: shareInfo.shareImg, // 分享图标
        success: function () {
          // 用户确认分享后执行的回调函数
        },
        cancel: function () {
          // 用户取消分享后执行的回调函数
        }
      });

      wx.onMenuShareAppMessage({
        title: shareInfo.shareTitle, // 分享标题
        desc: shareInfo.shareDesc, // 分享描述
        link: shareInfo.shareLink, // 分享链接
        imgUrl: shareInfo.shareImg, // 分享图标
        type: 'link', // 分享类型,music、video或link，不填默认为link
        dataUrl: '', // 如果type是music或video，则要提供数据链接，默认为空
        success: function () {
          // 用户确认分享后执行的回调函数
          alertBox('分享成功');
        },
        cancel: function () {
          // 用户取消分享后执行的回调函数
          alertBox('取消分享');
        }
      });
    });
    wx.error(function(res){
    });
  },

  //手机微信直播间分享参数设置
  zhiboShare: function(){
    var ZshareTitle = $('.forWxWxShare').attr('data-share-title'),
      ZshareDesc = $('.forWxWxShare').attr('data-share-desc'),
      ZshareImg = httpUrl($('.forWxWxShare').attr('data-share-img')),
      ZshareLink = window.location.href;      //分享的链接
    var shareInfo = {
      shareTitle: ZshareTitle,
      shareImg: ZshareImg,
      shareDesc: ZshareDesc,
      shareLink:ZshareLink
    };
    var url = window.location.href;
    var currentParam = url.split('?')[1];   //获取当前的url后面的参数

    var str = $('#globalParam').attr('data-action');
    var fidParam = strToObject(str)['fid'];    //获取fid

    var sendParam = '';
    if(currentParam){
      currentParam = currentParam.split('#')[0];
      sendParam = 'url='+encodeURIComponent(currentParam)+'&fid='+fidParam;
    }else{
      sendParam = 'fid='+fidParam;
    }
    var opts = {
      url:'//t.10jqka.com.cn/m/live/getWxSignPackage/?'+sendParam,
      callback:function(data){
        var obj = $.parseJSON(data);
        window.GAppid = obj.result.appId;
        window.Gtimestamp = obj.result.timestamp;
        window.GnonceStr = obj.result.nonceStr;
        window.Gsignature = obj.result.signature;
      }
    };
    wxShare.getSignParams(opts);
    wxShare.share(shareInfo);

  },

  // 获取签名参数
  getSignParams:function(opts){
    $.ajax({
      url:opts.url,
      type:'get',
      async:false,
      success:function(data){
        if( $.isFunction(opts.callback) ){
          opts.callback(data);
        }
      }
    });
  }

};
sns.wxShare = wxShare;

function isWeixin(){
  var ua = window.navigator.userAgent.toLowerCase();
  if(ua.match(/MicroMessenger/i) == "micromessenger"){
    return true;
  }else{
    return false;
  }
}
sns.isWeixin = isWeixin;

window.wxJsLoadFlag = false;
function citeWxJs(callback){
  var jsPath = '//res.wx.qq.com/open/js/jweixin-1.0.0.js';
  if(window.wxJsLoadFlag === true){
    wxShare.init();
    if($.isFunction(callback)){
      callback();
    }else{
      wxShare.zhiboShare();
    }
  }else{
    var script = document.createElement("script");
    script.setAttribute("type","text/javascript");
    script.setAttribute("src",jsPath);
    document.body.appendChild(script);
    script.onload = script.onreadystatechange = function() {
      if (!this.readyState || this.readyState === "loaded" || this.readyState === "complete" ) {
        window.wxJsLoadFlag = true;
        wxShare.init();
        if($.isFunction(callback)){
          callback();
        }else{
          wxShare.zhiboShare();
        }
      }
    };
  }
}
sns.citeWxJs = citeWxJs;

window.payFlag = false;
var wxPay = {
    init: function(){
        window.GAppidPay = '';
        window.GtimestampPay = '';
        window.GnonceStrPay = '';
        window.GsignaturePay = '';
        window.Gpackage = '';
        window.GsignType = '';
        window.GpaySign = '';
        window.GorderKey = '';//订单号
    },

    pay:function(payInfo,returnUrl){
        wx.config({
            debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
            appId: window.GAppidPay, // 必填，公众号的唯一标识
            timestamp: window.GtimestampPay, // 必填，生成签名的时间戳
            nonceStr: window.GnonceStrPay, // 必填，生成签名的随机串
            signature: window.GsignaturePay,// 必填，签名，见附录1
            jsApiList: ['checkJsApi','onMenuShareTimeline','onMenuShareAppMessage','onMenuShareQQ','onMenuShareWeibo',
                'onMenuShareQZone','hideMenuItems','showMenuItems','hideAllNonBaseMenuItem','showAllNonBaseMenuItem',
                'translateVoice','startRecord','stopRecord','onVoiceRecordEnd','playVoice','onVoicePlayEnd','pauseVoice',
                'stopVoice','uploadVoice','downloadVoice','chooseImage','previewImage','uploadImage', 'downloadImage',
                'openLocation','getLocation','hideOptionMenu','showOptionMenu','closeWindow',
                'scanQRCode','chooseWXPay','openProductSpecificView','addCard','chooseCard','openCard'] // 必填，需要使用的JS接口列表，所有JS接口列表见附录2
        });
        wx.error(function(res){});
        function onBridgeReady(payInfo){
            if(window.__wxjs_environment && window.__wxjs_environment === 'miniprogram'){ //小程序里
                var params = '?timeStamp='+payInfo.timeStamp+
                '&nonceStr='+payInfo.nonceStr+
                '&package='+encodeURIComponent(payInfo.package)+
                '&signType='+payInfo.signType+
                '&paySign='+payInfo.paySign;
                var path = '/pages/wxpay/wxpay'+params;
                wx.miniProgram.navigateTo({
                    url:path
                });
            }else{
                WeixinJSBridge.invoke(
                    'getBrandWCPayRequest',
                    {
                         appId:payInfo.appId,
                         timeStamp:payInfo.timeStamp,//支付前面时间戳
                         nonceStr:payInfo.nonceStr,//支付签名随机数
                         package:payInfo.package,//支付接口范回参数
                         signType:payInfo.signType,//签名方式
                         paySign:payInfo.paySign//支付签名
                    },
                    function(res){
                        if(res.err_msg == "get_brand_wcpay_request:ok" ) {//jshint ignore:line
                             var jumpUrl = returnUrl;

                             //轮询查看支付状态
                             var polling = setInterval(function(){
                                 var oData = {key:window.GorderKey};
                                 $.ajax({
                                     url: '/newcircle/pay/checkPayStatus/',
                                     type: 'GET',
                                     dataType: 'json',
                                     data: oData
                                 })
                                 .done(function(json) {
                                     if(json.errorCode === 0){//支付成功
                                         window.location.href = jumpUrl;
                                         clearInterval(polling);
                                     }else{
                                         // SNS.alertBox({"type":'error',"tipContent":json.errorMsg});
                                     }
                                 })
                                 .fail(function() {
                                     alertBox('支付失败');
                                 });
                             },1000);

                        }else{// 使用以上方式判断前端返回,微信团队郑重提示：res.err_msg将在用户支付成功后返回    ok，但并不保证它绝对可靠。
                             // alert(res.err_msg);//jshint ignore:line
                        }
                        window.payFlag = false;
                    }
                );
            }

        }
        if (typeof WeixinJSBridge == "undefined"){
           if( document.addEventListener ){
               document.addEventListener('WeixinJSBridgeReady', onBridgeReady, false);
           }else if (document.attachEvent){
               document.attachEvent('WeixinJSBridgeReady', onBridgeReady);
               document.attachEvent('onWeixinJSBridgeReady', onBridgeReady);
           }
        }else{
           onBridgeReady(payInfo);
        }
    },

    // 获取签名参数
    getSignParams:function(opts){
        $.ajax({
            url:opts.url,
            type:'get',
            async:false,
            success:function(data){
               if( $.isFunction(opts.callback) ){
                    opts.callback(data);
                }
            }
        });
    }
}

sns.wxPay = wxPay

window.wxJsPayLoadFlag = false;
function citeWxJsPay(callback,noneedFlag){
    if(!noneedFlag){
        if(window.payFlag){
            return false;
        }
    }
    // var jsPath = '//res.wx.qq.com/open/js/jweixin-1.0.0.js';
    // 带有小程序api
    var jsPath = '//res.wx.qq.com/open/js/jweixin-1.3.2.js';
    window.payFlag = true;
    if(window.wxJsPayLoadFlag === true){
        wxPay.init();
        if($.isFunction(callback)){
            callback();
        }else{
        }
    }else{
        var script = document.createElement("script");
        script.setAttribute("type","text/javascript");
        script.setAttribute("src",jsPath);
        document.body.appendChild(script);

        script.onload = script.onreadystatechange = function() {
            if (!this.readyState || this.readyState === "loaded" || this.readyState === "complete" ) {

                window.wxJsPayLoadFlag = true;
                wxPay.init();
                if($.isFunction(callback)){
                    callback();
                }else{
                }
            }
        };
    }
}
sns.citeWxJsPay = citeWxJsPay

/**
 * 动态引入js
 * @param {*} jsArr
 * @param {*} callback
 */
// function citeJS(jsArr,callback){
//     var HEAD = document.getElementsByTagName('head').item(0) || document.documentElement;
//     var j = [];
//     var jsLen = jsArr.length - 1;
//     var loadProcess = function(i){
//         j[i] = document.createElement('script');
//         j[i].setAttribute('type','text/javascript');
//         j[i].onload = j[i].onreadystatechange = function(){
//             if (!this.readyState || this.readyState === 'loaded' || this.readyState === 'complete' ){
//                 this.onload = this.onreadystatechange = null;
//                 if(i != jsLen){
//                     loadProcess(i + 1);
//                 }else if( typeof(callback) === 'function' ){
//                     callback();
//                 }
//             }
//         };
//         j[i].setAttribute('src',jsArr[i]);
//         HEAD.appendChild(j[i]);
//     };
//     loadProcess(0);
// }
// sns.citeJs = citeJS

//通用跳转方法【for视频】
function pageJumpLink(href){
	if(href){
		href = httpUrl(href);
		// if(!window.webviewSupportVideo && getApp() == 'ths'){ //不支持视频，同时在手抄内，做一把协议跳转
			// window.location.href = 'client.html?action=livevideo^url='+href+'^title=';
		// }else{
		window.location.href = href;
		// }
	}
}

sns.pageJumpLink = pageJumpLink

/**
 * 新webview打开连接
 */
function pageJumpWebview(url){
    var newUrl = httpUrl(url);
    if (getApp() === "futures") {
      window.location.href='client.html?webid=2804^url=' + newUrl;
    }
	if(getAppVersion()){
		var flagIpad = getPlatform() === 'iphone' && navigator.userAgent.indexOf('iPad') > -1;
		if(flagIpad){
			window.location.href = newUrl;
		}else{
			/*mode 只有安卓9.42以上才支持*/
			window.location.href = 'client.html?action=ymtz^url=' + newUrl + '^webid=2804^fontzoom=no^mode=new';
		}
	}else{
		window.location.href = newUrl;
	}
}
sns.pageJumpWebview = pageJumpWebview


/**
 * <AUTHOR> <<EMAIL>>
 * @date    14-7-21
 * @desc    获取同花顺客户端版本号
 * @eg      getAppVersion()
 */
function getAppVersion(){
    var u = navigator.userAgent;
    var gphoneIndex = u.indexOf("Hexin_Gphone");
    var iphoneIndex = u.indexOf("IHexin");
    if ( gphoneIndex > -1 ){
        var str = u.substr( gphoneIndex + 13 );
    }
    else if( iphoneIndex > -1 ){
        var str = u.substr( iphoneIndex + 7 );
    }
    else{
        return false;
    }
    var items = str.split(" ");
    return items[0];
}

sns.getAppVersion = getAppVersion

/**
 * 点击股票代码跳分时图界面
 * 参数 页面统计代码
 */
function goToTimerLine(pageStat){
	$('.wap-container').on('click','._dollar_',function(event){
		event.preventDefault();
		var $this = $(this);
		if(getApp() == 'ths'){
			event.stopPropagation(); //组织冒泡
			var code = $this.attr('data-code');
			var url = 'client://client.html?action=ymtz^webid=2205^stockcode='+code;
		 	hxmJumpNativeStat(pageStat+ '.stock','2205',{'to_scode':code});
			window.location.href = url;
			return false;
		}
	});
}
sns.goToTimerLine = goToTimerLine


/**
 * 判断夜间模式/白天模式
 * @return 1:夜间模式; 2:白天模式
 */
function getHxtheme(){
	var ua = navigator.userAgent.toLowerCase();
	var hxthemeIndex = ua.indexOf('hxtheme');
	var model = 0;

	if(hxthemeIndex > 0){
		model = + ua.substr(hxthemeIndex + 8,1);
	}

	if( model === 1 && document.body.className.indexOf('night') === -1){
		document.body.classList.add('night')
	}

	return model;
}
sns.getHxtheme = getHxtheme

//判断是否封禁,是否绑定手机号,合为一个接口
function isBanned(dtd,code){
  var paramCode = code ? '?code=' + code : ''
  $.ajax({
      url:'/m/stock/checkStatus/' + paramCode,
      type:'GET',
      dataType:'json',
      success:function(result){
          // result.errorCode = -16;
          if(result.errorCode === 0){
              dtd.resolve('normal');
          }else if(result.errorCode == -16){ //封禁
              // if(getApp() == 'ths' && getPlatform() == 'iphone'){
              //     showAppealBox(statid);
              // } else if(getApp() == 'ths' && getPlatform() == 'gphone'){
              //     showAppealBox('g_fenshi_lungu');
              // } else{
              //     showAppealBox();
              // }
              alertBox(result.errorMsg);
              dtd.resolve('banned');
          }else if(result.errorCode == -99){ //未绑定手机号
              dtd.resolve('tel');
          }else{
              alertBox(result.errorMsg);
              dtd.resolve('error');
          }
      },
      error: function(){
          alertBox('网络不给力，请重试！');
          dtd.reject();
      }
  });
  return dtd;
}

sns.isBanned = isBanned

/**
 * promise+ajax封装绑定手机号
 */
function ajaxPromise(){
  return new Promise((resolve,reject)=>{
    $.ajax({
      url:'//t.10jqka.com.cn/api.php?method=newcircle.bindPhoneCheck&return=jsonp&cookie='+document.cookie,
      type:'GET',
      dataType:'jsonp',
      success:function(res){
        resolve(res)
      },
      error: function(err){
        reject(err)
      }
    });
  })
}
sns.ajaxPromise = ajaxPromise

/**
 * 获取客户端版本信息等
 * 基于现在使用习惯 getApp等集成
 * return {
 *  platform:'iphone/gphone',
 *  version: 103080
 *  app: 'ths/weixin/gsjl/'
 * }
 * 后续可以拓展 isWeixin isThs isSupportShare isSupportShare ...
 */
function getAppInfo() {
  let ua = navigator.userAgent
  let version='',app='',str='',iJiJinVersion = ''

  let gphoneIndex = ua.indexOf("Hexin_Gphone");
  let iphoneIndex = ua.indexOf("IHexin/");

  let gsjlIphoneIndex = ua.indexOf('IHexin_gsjl');
  let tzzbIphoneIndex = ua.indexOf('IHexin_xcs');
  let tzzbGphoneIndex = ua.indexOf('GHexin_xcs');
  let ljIndex = ua.indexOf('Hexin_Metal');
  let wexin = ua.toLowerCase().indexOf('micromessenger');
  let ifindIphoneIndex = ua.indexOf('ifind_iphone');
  let ifindGphoneIndex = ua.indexOf('ifind_gphone');
  let iJijinGphoneIndex = ua.indexOf('GphoneIjiJin/V');
  let iJijinIphoneIndex = ua.indexOf('IphoneIJiJinAPP/');

  // 获取平台系统
  let platform = ua.indexOf("iPhone") > -1 || ua.indexOf("Mac") > -1 || ua.indexOf("iPad") > -1 ? 'iphone' : 'gphone'

  // 获取客户端版本号
  let gphoneversion = 1;
  if ( gphoneIndex > -1 ){
    str = ua.substr( gphoneIndex + 13 );
    gphoneversion = parseInt(ua.match(/Hexin_Gphone\/(.*)/)[1].split('.')[0]);
  }else if( iphoneIndex > -1 ){
    str = ua.substr( iphoneIndex + 7 );
  }else if( iJijinGphoneIndex > -1 ){
    iJiJinVersion = ua.substr( iJijinGphoneIndex + 14 );
    iJiJinVersion = iJiJinVersion ? parseInt(iJiJinVersion.split(" ")[0].split(".").join("")) : '';
  }else if( iJijinIphoneIndex > -1 ){
    iJiJinVersion = ua.substr( iJijinIphoneIndex + 16 );
    iJiJinVersion = iJiJinVersion ? parseInt(iJiJinVersion.split(" ")[0].split(".").join("")) : '';
  }
  version = str ? parseInt(str.split(" ")[0].split(".").join("")) : '';

  // 获取客户端类型
  // 目前股市教练版本是1.X.X
  // 同花顺app版本是9.X.X
  if( gsjlIphoneIndex > -1 || (gphoneIndex > -1 && gphoneversion < 7) ){
    // 在股市教练APP中
    app = 'gsjl';
  }else if( tzzbIphoneIndex > -1 || tzzbGphoneIndex > -1 ){
    // 在投资账本APP中
    app = 'tzzb';
  }else if( ljIndex > -1 ){
    // 猎金App中
    app = 'liejin';
  }else if( iphoneIndex > -1 || (gphoneIndex > -1 && gphoneversion > 7) ){
    // 在同花顺APP中
    app = 'ths';
  }else if( wexin > -1 ){
    // 微信中
    app = 'weixin';
  }else if(ifindIphoneIndex > -1 || ifindGphoneIndex > -1){
    // ifind
    app = 'ifind';
  }else if(iJijinGphoneIndex > -1 || iJijinIphoneIndex > -1){
    // ifind
    app = 'iJiJin';
  }else{
    app = 'other';
  }

  return {
    platform,
    version,
    app,
    iJiJinVersion
  }
}
sns.getAppInfo = getAppInfo

function loadImg(url, fn,errorfn){
  var img = new Image();
  img.src = url;
  if (img.complete) {
      fn.call(img);
  } else {
      img.onload = function() {
          fn.call(img);
      };
      img.onerror = function(){
        errorfn ? errorfn.call() : '';
      };
  }
}

sns.loadImg = loadImg

//论股堂缩放后的图片不剪裁居中显示到图片框
function scaleImg(Img, maxWidth, maxHeight) {
  //原图片宽高比例大于 图片框宽高比例
  if (maxWidth/ maxHeight  <= Img.width / Img.height) {
      $(Img).width(maxWidth);
  }
  else {   //原图片宽高比例 小于 图片框宽高比例
   $(Img).height(maxHeight);
  }
}

sns.scaleImg = scaleImg

const intervalQueue = (() => {
  // const isIOS = window.getPlatform().indexOf('iphone') > -1;
  // const handle = isIOS ? window.registerWebHandler : window.registerWebListener;
  let isCurPageView = true;
  const fnArr = [] // 处理轮循相关的
  let timers = []
  const onShowArr = [] // 处理非轮循相关
  const onHideArr = []

  const pushQueue = (fn, time, firstUpdate = true) => {
    fnArr.push({
      fn,
      time,
      firstUpdate
    })
  }

  const pushHandle = (fn, type = 'onShow') => {
    if (type === 'onHide') {
      const index = onHideArr.findIndex(hideFn => hideFn.name === fn.name)
      if (index !== -1) {
        onHideArr[index] = fn
      } else {
        onHideArr.push(fn)
      }
    } else {
      const index = onShowArr.findIndex(showFn => showFn.name === fn.name)
      if (index !== -1) {
        onShowArr[index] = fn
      } else {
        onShowArr.push(fn)
      }
    }
  }

  const startShowQueue = () => {
    onShowArr.map(fn => {
      fn()
    })
  }

  const startHideQueue = () => {
    onHideArr.map(fn => {
      fn()
    })
  }

  // 初始化
  const initQueue = () => {
    fnArr.map((fObj) => {
      const timer = setInterval(fObj.fn, fObj.time)
      timers.push(timer)
    })
  }

  const stopQueue = () => {
    isCurPageView = false
    timers.map((t) => clearInterval(t))
    timers = []
  }

  const startQueue = () => {
    if (isCurPageView) {
      return
    }
    isCurPageView = true
    fnArr.map((fObj) => {
      fObj.firstUpdate && fObj.fn()
      const timer = setInterval(fObj.fn, fObj.time)
      timers.push(timer)
    })
  }

  return {
    pushQueue,
    initQueue,
    stopQueue,
    pushHandle,
    startShowQueue,
    startHideQueue,
    startQueue
  }
})()

sns.intervalQueue = intervalQueue

// 通过请求队列来控制低网条件下,快速点击tab时始终只渲染最后一次请求
const ajaxQueue = (() => {
  const obj = {};
  const setFlag = sceneName => {
    if ( !obj[sceneName] ) {
      obj[sceneName] = 1;
    } else {
      obj[sceneName]++;
    }
  }
  const getFlag = sceneName => {
    const flag = obj[sceneName];
    return flag;
  };
  const clearFlag = sceneName => {
    obj[sceneName] = 1;
  };
  return {
    setFlag,
    getFlag,
    clearFlag,
  };
})();
sns.ajaxQueue = ajaxQueue;

export {
  getUrlParams,
  initFontSize,
  getApp,
  httpUrl,
  wxShare,
  isWeixin,
  // citeJs,
  citeWxJs,
  citeWxJsPay,
  sns
};
