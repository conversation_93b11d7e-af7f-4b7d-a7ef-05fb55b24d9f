import {
  getApp,
} from './sns.js';

class hxOptionMenu {

  constructor() {
    this.setting = {
      shareInfo: {
        url: '',
        title: '',
        content: '',
        thumbImageUrl: '',
        logoImageUrl: ''
      },
      pageId: '',
      menuResponse(){}
    };
  }


  config(options) {
    Object.assign(this.setting, options);
    return this;
  }

  init() {
    this.initProtocol();
    this.initAppListen();
  }

  initProtocol(){
    try {
      callNativeHandler('NotifyNativeEventToWeb', '');
      callNativeHandler('notifyWebHandleEvent', {
        method: 'HXBottomOptionMenu',
        params: {
          show: 1,
          optionConfig: {
            shareInfo: this.setting.shareInfo
          }
        }
      });
    } catch (e) {
      console.log(e);
    }
  }

  initAppListen() {
    const self = this;
    let pid = '';
    try {
      registerWebHandler('notifyWebHandleEvent', function(data){
        if (data.params.type && data.params.type == 'share') {
          hxmClickStat(`${self.setting.pageId}.share.${data.params.name}`);
          self.controlShare(data);
        } else if(data.params.name){
          self.controlMenu(data);
        }
        if (data.params && data.params.name == 'menu') {
          if (getApp() === 'ths') {
            pid = `free_zixun_zhuti_${window.componentList.id}`;
          } else {
            pid = `share_free_zixun_zhuti_${window.componentList.id}`;
          }
          window.shareConfig = {
            type: 'topShare',
            pageId: pid,
            componentId: 'function'
          };
        }
      });
    } catch (e) {
      console.log(e);
    }
  }

  controlShare(data) { //控制分享的点击功能
    const operateType = data.params.name;

    if (this.setting.menuResponse && typeof this.setting.menuResponse === 'function') {
      this.setting.menuResponse(operateType);
    }

    switch (operateType) { // 分享menu的显示与隐藏
    case 'wxpyq':
      break;
    case 'wxhy':
      break;
    case 'wbsina':
    case 'qqkj':
    case 'qqhy':
    case 'hyperlink':
      //其他操作
      break;
    }
  }

  controlMenu(data){ //控制...的操作与显示
    const operateType = data.params.name;

    if (this.setting.menuResponse && typeof this.setting.menuResponse === 'function') {
      this.setting.menuResponse(operateType);
    }

    switch (operateType) { // 分享menu的显示与隐藏
    case 'menu':
      this.showOptionMenu(operateType);
      break;
    case 'close':
    case 'mask':
      this.hideOptionMenu(operateType);
      break;
    }
  }

  showOptionMenu() {
    try {
      callNativeHandler('notifyWebHandleEvent', {
        method: 'HXBottomOptionMenuShow',
      });
    } catch (e) {
      // console.log(e)
    }
  }

  hideOptionMenu() {
    try {
      callNativeHandler('notifyWebHandleEvent', {
        method: 'HXBottomOptionMenuHide',
      });
    } catch (e) {
      // console.log(e)
    }
  }
}

export default new hxOptionMenu();
