import {sns} from '@/common/js/sns.js';

class Interface {
  constructor(){
    this.followChannelFlag = false; //关注提交
    this.clickOriginGo = '//bbsclick.10jqka.com.cn/'; // bbs获取数据的接口
  }
  /*
	desc:关注频道函数接口api
	params:{
			followType:'add', // add关注频道、del取关频道
			type:'get',	//默认get
			oData:{fid:}, 	//关注接口参数:fid:频道id
			followBeforeSend:function(){}, //请求发送前回调
			followSuccess:function(){}, //关注请求成功回调
			followError:function(){}, //请求失败
			followComplete:function(){} //请求完成
		}
	author:lixinyan
	date:2017-7-19
*/
  followChannel(options){
    const self = this;
    //检测登录
    if(!sns.isLogin()){
      sns.doLogin();
      return false;
    }

    //接口防重复提交
    if(this.followChannelFlag){
      return false;
    }
    let opts = {
      followType:'add',
      type:'get',
      oData:{}, 	//参数 fid:频道id
      followBeforeSend(){}, //请求发送前
      followSuccess(){}, //请求成功回调
      followError(){
        sns.alertBox('操作失败');
      }, //请求失败
      followComplete(){} //请求完成
    };
    opts = $.extend(opts, options);
    const url = sns.urlPrefix('/m/channel/handleChannel/');

    if(opts.followType == 'del'){
      opts.oData.opt = 'del';
    }

    $.ajax({
      url,
      type: opts.type,
      dataType: 'json',
      data: opts.oData,
      beforeSend(){
        self.followChannelFlag = true;
        if($.isFunction(opts.followBeforeSend)){
          opts.followBeforeSend(opts);
        }
      },
      success(json){
        if($.isFunction(opts.followSuccess)){
          opts.followSuccess(json,opts);
        }
      },
      error(){
        if($.isFunction(opts.followError)){
          opts.followError(opts);
        }
      },
      complete(){
        self.followChannelFlag = false;
        if($.isFunction(opts.followComplete)){
          opts.followComplete(opts);
        }
      }
    });
  }

  /*
  * 仅action = get  使用
  * 获取单个key的数据
  * 不能获取批量数据
  * field = clicks 点击量提取, 页面访问量
  * field = likes 获取点赞量
  */
  clickGoAjax(key, successCallback, field,inc){
    const originfield = field || 'clicks';
    const incStr = inc ? '&inc=1' : '';
    const url = `${this.clickOriginGo  }get?app=sns${ incStr }&field=${  originfield  }&key=${  key}`;

    $.ajax({
      url,
      type: 'get',
      dataType: 'jsonp',
      success(json){
        if(successCallback && $.isFunction(successCallback)){
          successCallback(json);
        }
      }
    });
  }

  /*
  * 仅action = get  使用
  * 获取批量数据
  * field = clicks 点击量提取
  * field = likes 获取点赞量
  * log 不判断是否之前已点赞 0       判断是否之前已点赞 1
  */
  clickListGoAjax(key, successCallback, field,log){
    const originfield = field || 'clicks';
    const originlog = log || 0;
    const url = `${this.clickOriginGo  }getlist?app=sns&field=${  originfield  }&key=${  key  }&log=${  originlog}`;

    $.ajax({
      url,
      type: 'get',
      dataType: 'jsonp',
      success(json){
        if(successCallback && $.isFunction(successCallback)){
          successCallback(json);
        }
      }
    });
  }

  /*
  * 功能： 点赞 普遍用来增加点赞
  * field = likes 获取点赞量
  * errorcode == -4 已点赞
  * 默认为获取点赞数量
  */
  zanAjax(key, successCallback,add,field){
    const isAdd = add || true;
    const action = isAdd ? 'inc':'get';
    const originfield = field || 'likes';
    const url = `${this.clickOriginGo + action  }?app=sns&field=${ originfield }&key=${  key}`;

    $.ajax({
      url,
      type: 'get',
      dataType: 'jsonp',
      success(json){
        if(successCallback && $.isFunction(successCallback)){
          successCallback(json);
        }
      }
    });
  }

  /**
   *  @Function 获取单个key的点数或者获取一个列表中key的点赞数
   * 	@param obj
   * 	    key: ['key1','key2','key3'] | 'key1,key2,key3' | 'key1'
   * 	    cd: function(){}
   * 	    hasZan: false【不判断之前是否已点赞】 | true【判断之前是否已点赞】
   * 	@notice hasZan只在获取list的赞数时生效
   */
  getLikes(obj) {
    const option = {
      key: [],
      cb: null,
      hasZan: false
    };
    $.extend(true, option, obj);

    const originlog = option.hasZan ? 1 : 0;
    let url = this.clickOriginGo;

    if (typeof option.key === 'string') {
      if (option.key.indexOf(',') === -1) {  // 单个key
        url += `${'get?app=sns' +'&field=likes'+'&key='}${  option.key}`;
      } else {  // key列表
        url += `${'getlist?app=sns&field=likes' + '&key='}${  option.key  }&log=${  originlog}`;
      }

    } else if(typeof option.key === 'object' && option.key instanceof Array) {
      url += `${'getlist?app=sns&field=likes' + '&key='}${  option.key  }&log=${  originlog}`;
    }

    $.ajax({
      url,
      type: 'get',
      dataType: 'jsonp',
      success(json){
        if(option.cb && $.isFunction(option.cb)){
          option.cb(json);
        }
      }
    });
  }

  /**
   *  @Function 获取单个key的阅读数或者获取一个列表中key的阅读数
   * 	@param obj
   * 	    key ['key1','key2','key3'] | 'key1,key2,key3' | 'key1'
   * 	    cd callback
   * 	    add false【只获取阅读数】 | true【获取加1之后的阅读数】
   * 	@notice add只针对单个key有效
   */
  getViewNum(obj) {
    const option = {
      key: [],
      cb: null,
      add: false,
    };
    $.extend(true, option, obj);

    const incStr = option.add ? '&inc=1' : '';
    let url = this.clickOriginGo;

    if (typeof option.key === 'string') {
      if (option.key.indexOf(',') === -1) {  // 单个key
        url += `get?app=sns${ incStr }&field=clicks`+`&key=${  option.key}`;
      } else {  // key列表
        url += `${'getlist?app=sns&field=clicks' + '&key='}${  option.key}`;
      }

    } else if(typeof option.key === 'object' && option.key instanceof Array) {
      url += `${'getlist?app=sns&field=clicks' + '&key='}${  option.key.join(',')}`;
    }

    $.ajax({
      url,
      type: 'get',
      dataType: 'jsonp',
      success(json){
        if(option.cb && $.isFunction(option.cb)){
          option.cb(json);
        }
      }
    });
  }

  /** @Function 对单个key点赞
   * 	@param obj
   * 	    key: 'key1'
   * 	    cb: callback
   * 	@notice 点赞是必须是登录状态
   */
  Zan(obj) { // 单个点赞
    const option = {
      key: '',
      cb: null,
      add: true,
    };
    $.extend(true, option, obj);
    const action = option.add ? 'inc':'get';
    const url = `${this.clickOriginGo + action  }?app=sns&field=likes` +`&key=${  option.key}`;

    $.ajax({
      url,
      type: 'get',
      dataType: 'jsonp',
      success(json){
        if(option.cb && $.isFunction(option.cb)){
          option.cb(json);
        }
      }
    });
  }

  /**
   * 开户方法
   * @return {[type]} [description]
   * action 签约相关参数
   */
  openAccount(action){
    const self = this;

    if(!sns.isLogin()){
      sns.doLogin();
      return false;
    }
    if(sns.getApp() != 'ths'){//手炒外
      sns.alertBox('请前往同花顺APP进行开户操作');
      return false;
    }
    if(!getAppVersion()){
      return false;
    }
    if(getPlatform() == 'iphone'){
      //iphone手抄，同时大于等于10.20.25的版本，可以使用收藏
      const iphoneVersion = +'10.20.25'.split('.').join('');
      const version = +getAppVersion().split('.').join('');
      if(version < iphoneVersion){
        sns.alertBox('当前版本不支持该功能，请前往升级');
        return false;
      }
    }
    const strObj = sns.strToObject(action);

    callNativeHandler(
      'startPlugin',
      {
        'params': strObj,
        'cname': 'com.hexin.plat.kaihu.activity.MainActi',
        'pname': 'com.hexin.plat.kaihu',
        'scheme': 'kaihuplugin'
      },
      function(data) {}
    );
  }

  /**
   * 初始化微信分享
   * option {
   *     shareTitle: '',
   *     shareImg: '',
   *     shareDesc: '',
   *     shareLink: ''
   *  }
   */
  initWxShare(option){
    const oData = {
      shareTitle: '',
      shareImg: '',
      shareDesc: '同花顺分享',
      shareLink: window.location.href
    };
    $.extend(true, oData, option);
    sns.citeWxJs(function(){
      const shareInfo = {
        shareTitle: oData.shareTitle,
        shareImg  : oData.shareImg,
        shareDesc : oData.shareDesc,
        shareLink : oData.shareLink
      };
      const opts = {
        url:`//t.10jqka.com.cn/m/game/getWxSignPackage/?url=${encodeURIComponent(oData.shareLink)}`,
        callback(data){
          const obj = $.parseJSON(data);
          window.GAppid = obj.result.appId;
          window.Gtimestamp = obj.result.timestamp;
          window.GnonceStr = obj.result.nonceStr;
          window.Gsignature = obj.result.signature;
        }
      };
      sns.wxShare.getSignParams(opts);
      sns.wxShare.share(shareInfo);
    });
  }

  /**
   * @author: yaoyongfang
   * @date: 2017-07-19
   * @func: 分享协议通用方法
   * @params: shareObj = {
 *				type: '1' , 分享类型 1 网页 2 分享图片 3分享app（目前不支持）
 *				title: '标题'，
 *				content: '内容文案'
 *				url: '分享网页的地址'
 *				bmpRes: '1' 图片来源 1 网络 2 来自客户端截图 3 来自客户端默认图片
 *				bmpUrl: '图片地址'
 *				actionKey: '1' 分享内容key，用于客户端统计
 *			}
   */
  hxShare(params){
    const shareObj = {
      type:'1',
      title:'',
      content:'',
      bmpRes:'1',
      bmpUrl:'//i.thsi.cn/sns/circle/wapcircle/img2/calfgame/ths_logo.png',
      url:'',
      actionKey:'1'
    };
    $.extend(shareObj, params);
    shareObj.bmpUrl = sns.httpUrl(shareObj.bmpUrl); //如果非正常图片url，导致协议调用失败
    shareObj.url = sns.httpUrl(shareObj.url);
    const shareParam = JSON.stringify(shareObj);
    callNativeHandler('hexinShare',shareParam,function(){});
  }

  /**
   * 同步发言权限接口 返回对象
   * tips：为了兼容ios无法在异步操作里自动focus
   */
  hasBindPhoneAsync(){
    const canLiveObj = {
      isBindPhone:false,
      result:{}
    };
    $.ajax({
      url: '/m/live/canLive/',
      type:'GET',
      async: false,
      dataType:'json',
      success(result){
        if(result.errorCode === 0){
          canLiveObj.isBindPhone = true;
        }
        canLiveObj.result = result;
      },
      error(){
        canLiveObj.isBindPhone = false;
      }
    });
    return canLiveObj;
  }

  getHxtheme(){
    const ua = navigator.userAgent.toLowerCase();
    const hxthemeIndex = ua.indexOf('hxtheme');
    let model = 0;

    if(hxthemeIndex > 0){
      model = + ua.substr(hxthemeIndex + 8,1);
    }

    if( model === 1 && !$('body').hasClass('night') ){
      $('html,body').addClass('night');
    }

    return model;
  }

  /**
   * 是否封禁和手机号 合并在一起
   */
  isBaned(oData){
    return new Promise((resolve, reject) => {
      $.ajax({
        url: '//t.10jqka.com.cn/api.php?method=newgroup.checkStatus&return=jsonp',
        type:'GET',
        data: oData,
        dataType:'jsonp',
        success(result){
          resolve(result);
        },
        error(result){
          reject(result);
        }
      });
    });
  }
  //获取作者数据
  getAuthorAjax() {
    return new Promise((resolve, reject) => {
      $.ajax({
        url:
              process.env.NODE_ENV === 'development'
                ? 'http://yapi.myhexin.com/yapi/mock/3664/lgt/tsh/profile/get_profile'
                : '//t.10jqka.com.cn/lgt/tsh/profile/get_profile',
        type: 'GET',
        data: {
          spread: window.initData.stockcode[0]
        },
        dataType: 'json',
        success: json => {
          resolve(json);
        },
        error: errorMsg => {
          reject(errorMsg);
        }
      });
    });
  }
}


const snsInterface = new Interface();

export {
  snsInterface
};
