export function groupPromiseAll(funcTree, paramList) {
  const list = funcTree.map(func => {
    const type = Object.prototype.toString.call(func).slice(8, -1);
    if (type === 'Function') {
      return func.call(this, paramList);
    } else if (type === 'Array') {
      return groupPromiseAll(func, paramList);
    }
  });
  return new Promise(resolve => {
    Promise.all(list).then(() => {
      resolve(paramList);
    }).catch(() => {
      resolve(paramList);
    });
  });
}

/**
 * url search 参数
 * @type {Map<string, string>}
 */
export const search = new Map(window.location.search.slice(1).split('&').map(c => c.split('=')));
