@charset "utf-8";
html,body{font-family: Arial , 'PingFangSC-Regular' !important;}
a{text-decoration: none;-webkit-tap-highlight-color:transparent !important;}
*{-webkit-tap-highlight-color:transparent !important;}
.h100{height: 100%;}
.bgfff{background-color: #fff !important;}
.cfff{color: #fff;}
.c333{color: #333!important;}
.c666{color: #666 !important;}
.c444{color: #444!important;}
.c777{color: #777 !important;}
.c999{color: #999 !important;}
.cccc{color: #ccc !important;}
.blacktext{color: #323232 !important;}
.graytext{color: #949494 !important;}
.redtext{color: #e93030 !important;}
.yellowtext{color: #fea31e!important;;}
.greentext{color: #009900 !important;}
.bluetext{color: #2861a8 !important;}
.lightbluetext{color: #4691ee !important;}
.yellowtext{color: #fea31e;}
.bts{border-top: 1px solid #eee;}
.bbs{border-bottom: 1px solid #eee;}
.bluelink{color: #2861a8 !important;}
.lightblue{color: #4691ee !important;}
.redborder{border-color: #e93030 !important;}
.orangetext{color: #ff801a!important;}

.hidden{display: none!important;}

.por{position: relative;}
.poa{position: absolute;}

.w90{width: 90%;margin: 0 auto;}
.mb0{margin-bottom: 0 !important;}
.mt0{margin-top: 0 !important;}

.mt5{margin-top: 5px !important;}
.mr5{margin-right: 5px !important;}
.mb5{margin-bottom: 5px !important;}
.ml5{margin-left: 5px !important;}

/*补充margin和padding*/

.mt5{margin-top: 5px !important;}.mt5rem{margin-top: 0.05rem !important;}
.mr5{margin-right: 5px !important;}.mr5rem{margin-right: 0.05rem !important;}
.mb5{margin-bottom: 5px !important;}.mb5rem{margin-bottom: 0.05rem !important;}
.ml5{margin-left: 5px !important;}.ml5rem{margin-left: 0.05rem !important;}

.mt10{margin-top: 10px !important;}.mt10rem{margin-top: 0.1rem !important;}
.mr10{margin-right: 10px !important;}.mr10rem{margin-right: 0.1rem !important;}
.mb10{margin-bottom: 10px !important;}.mb10rem{margin-bottom: 0.1rem !important;}
.ml10{margin-left: 10px !important;}.ml10rem{margin-left: 0.1rem !important;}

.mt15{margin-top: 15px !important;}.mt15rem{margin-top: 0.15rem !important;}
.mr15{margin-right: 15px !important;}.mr15rem{margin-right: 0.15rem !important;}
.mb15{margin-bottom: 15px !important;}.mb15rem{margin-bottom: 0.15rem !important;}
.ml15{margin-left: 15px !important;}.ml15rem{margin-left: 0.15rem !important;}

.mt20{margin-top: 20px !important;}.mt20rem{margin-top: 0.2rem !important;}
.mr20{margin-right: 20px !important;}.mr20rem{margin-right: 0.2rem !important;}
.mb20{margin-bottom: 20px !important;}.mb20rem{margin-bottom: 0.2rem !important;}
.ml20{margin-left: 20px !important;}.ml20rem{margin-left: 0.2rem !important;}

.mt25{margin-top: 25px !important;}.mt25rem{margin-top: 0.25rem !important;}
.mr25{margin-right: 25px !important;}.mr25rem{margin-right: 0.25rem !important;}
.mb25{margin-bottom: 25px !important;}.mb25rem{margin-bottom: 0.25rem !important;}
.ml25{margin-left: 25px !important;}.ml25rem{margin-left: 0.25rem !important;}

.mt30{margin-top: 30px !important;}.mt30rem{margin-top: 0.3rem !important;}
.mr30{margin-right: 30px !important;}.mr30rem{margin-right: 0.3rem !important;}
.mb30{margin-bottom: 30px !important;}.mb30rem{margin-bottom: 0.3rem !important;}
.ml30{margin-left: 30px !important;}.ml30rem{margin-left: 0.3rem !important;}


.pt5{padding-top: 5px !important;}.pt5rem{padding-top: 0.05rem !important;}
.pr5{padding-right: 5px !important;}.pr5rem{padding-right: 0.05rem !important;}
.pb5{padding-bottom: 5px !important;}.pb5rem{padding-bottom: 0.05rem !important;}
.pl5{padding-left: 5px !important;}.pl5rem{padding-left: 0.05rem !important;}

.pt10{padding-top: 10px !important;}.pt10rem{padding-top: 0.1rem !important;}
.pr10{padding-right: 10px !important;}.pr10rem{padding-right: 0.1rem !important;}
.pb10{padding-bottom: 10px !important;}.pb10rem{padding-bottom: 0.1rem !important;}
.pl10{padding-left: 10px !important;}.pl10rem{padding-left: 0.1rem !important;}

.pt15{padding-top: 15px !important;}.pt15rem{padding-top: 0.15rem !important;}
.pr15{padding-right: 15px !important;}.pr15rem{padding-right: 0.15rem !important;}
.pb15{padding-bottom: 15px !important;}.pb15rem{padding-bottom: 0.15rem !important;}
.pl15{padding-left: 15px !important;}.pl15rem{padding-left: 0.15rem !important;}

.pt20{padding-top: 20px !important;}.pt20rem{padding-top: 0.2rem !important;}
.pr20{padding-right: 20px !important;}.pr20rem{padding-right: 0.2rem !important;}
.pb20{padding-bottom: 20px !important;}.pb20rem{padding-bottom: 0.2rem !important;}
.pl20{padding-left: 20px !important;}.pl20rem{padding-left: 0.2rem !important;}

.pt25{padding-top: 25px !important;}.pt25rem{padding-top: 0.25rem !important;}
.pr25{padding-right: 25px !important;}.pr25rem{padding-right: 0.25rem !important;}
.pb25{padding-bottom: 25px !important;}.pb25rem{padding-bottom: 0.25rem !important;}
.pl25{padding-left: 25px !important;}.pl25rem{padding-left: 0.25rem !important;}

.pt30{padding-top: 30px !important;}.pt30rem{padding-top: 0.3rem !important;}
.pr30{padding-right: 30px !important;}.pr30rem{padding-right: 0.3rem !important;}
.pb30{padding-bottom: 30px !important;}.pb30rem{padding-bottom: 0.3rem !important;}
.pl30{padding-left: 30px !important;}.pl30rem{padding-left: 0.3rem !important;}

.lh1{line-height: 1 !important}
.lh2{line-height: 2 !important;text-indent: 0;}
.lh18{line-height: 18px;}

.bluebg{background-color: #4691ee !important;}
.yellowbg{background-color: #ffab32 !important;}
.graybg{background-color: #e7e7e7 !important;}
.redbg{background-color: #c73138 !important;}
.nightbg{background-color: #2a2a2d !important;}
.fz9{font-size: 9px !important;}
.fz10{font-size: 10px !important;}
.fz12{font-size: 12px !important;}
.fz14{font-size: 14px !important;}
.fz15{font-size: 15px !important;}
.fz16{font-size: 16px !important;}
.fz18{font-size: 18px !important;}
.ti2{text-indent: 2em;}
.rem8{font-size: .08rem !important;}
.rem10{font-size: .10rem !important;}
.rem12{font-size: .12rem !important;}
.rem13{font-size: .13rem !important;}
.rem14{font-size: .14rem !important;}
.rem15{font-size: .15rem !important;}
.rem16{font-size: .16rem !important;}
.rem18{font-size: .18rem !important;}
.rem20{font-size: .20rem !important;}
.rem22{font-size: .22rem !important;}
.rem24{font-size: .24rem !important;}
.rem26{font-size: .26rem !important;}
.rem28{font-size: .28rem !important;}
.rem30{font-size: .30rem !important;}
.rem32{font-size: .32rem !important;}
.rem34{font-size: .34rem !important;}
.rem36{font-size: .36rem !important;}
.rem38{font-size: .38rem !important;}
.rem40{font-size: .40rem !important;}
.ofh{overflow: hidden;}
.ovh{overflow: hidden;}
.tac{text-align: center;}
.tar{text-align: right;}
.tal{text-align: left;}
.taj{text-align: justify;}
.center{margin: 0 auto;}
.kong{text-align: center;font-size: 14px;color: #777;padding: 30px 0;}

.btn{text-align: center;color: #fff;height: 24px;line-height: 24px;border-radius: 4px;font-size: 12px;display: inline-block;*display: inline;zoom:1;}

.btn-h44{height: 44px;line-height: 44px;}
.btn-h40{height: 40px;line-height: 40px;}
.btn-h30{height: 30px;line-height: 30px;}
.w100{width: 100%;}
.p010{padding: 0 10px;}
.p030{padding: 0 30px;}
.probox{padding: 20px 10px 50px 10px;}
.ellipsis{overflow: hidden; white-space: nowrap; text-overflow: ellipsis; }

.pr010{padding: 0 0.10rem;}
.pr016{padding: 0 0.16rem;}
.p016{padding: 0 0.16rem;}
.m016{margin: 0 0.16rem;}

.db{display: block;}
.dib{display: inline-block;}

//font-weight
.fwb{ font-weight: 700; }

.fl{float: left;}
.fr{float: right;}
.clearfix:before, .clearfix:after { content: ""; display: table; }
.clearfix:after { clear: both; }
.clearfix { zoom: 1; clear: both; }
.vat{vertical-align: top !important;}
.vam{vertical-align: middle!important;}
.wdwrap{word-break: break-word;word-wrap:break-word;}
.textwrap{overflow: hidden; white-space: nowrap; text-overflow:ellipsis;}
/*文字两行显示*/
.single-line {
overflow: hidden;
white-space: nowrap;
text-overflow: ellipsis;
}
.multiple-line(@line) {
overflow: hidden;
text-overflow: ellipsis;
display: -webkit-box;
-webkit-line-clamp: @line;
-webkit-box-orient: vertical;
}
.line2wrap{overflow: hidden; display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; word-break: break-word;}
.line3wrap{overflow: hidden; display: -webkit-box; -webkit-line-clamp: 3; -webkit-box-orient: vertical; word-break: break-word;}

.rowflex{display: flex;display: -webkit-flex;flex-direction: row;display: -webkit-box;-webkit-box-orient:horizontal;}
.columnflex{display: flex;display: -webkit-flex;flex-direction: column;display: -webkit-box;-webkit-box-orient:vertical;}
/*测试一下*/ 
.flex1{-webkit-box-flex:1;flex:1;-webkit-flex:1;}
.flex2{-webkit-box-flex:2;flex:2;-webkit-flex:2;}
.flex3{-webkit-box-flex:3;flex:3;-webkit-flex:3;}
.m0 img{margin: 0 auto !important;}

/*alertbox*/
.alertbox{max-width:50%;padding:8px 48px;text-align: center;font-size: 32px;position: fixed;top: 25%;left: 25%;z-index: 1202;border-radius: 10px;line-height: 2;line-height: 20px;opacity: 0.96;}
.fontinit .alertbox{line-height: 0.4rem;border-radius: 0.2rem;padding-top: 0.08rem;padding-bottom: 0.08rem;}
/*蒙层后添加弹框，修改成1202*/
.alertboxmsg{position: relative;z-index: 1;color: #fff;}
.alertboxbg{position: absolute;left: 0;top: 0;width: 100%;height: 100%;background-color: #000;opacity: 0.8;border-radius: 8px;}

.night{
  .confirm{background: #252528}
  .confirm-content{
    border-bottom-color: #333;
    color: #d2d2d2
  }
  .confirm-left{
    border-right-color: #333;
    color: #8e8e8e;
  }
  .confirm-right{
    color: #8e8e8e
  }
}

.confirm{z-index: 1200;border-radius: 10px;position: fixed;width: 60%;left: 20%;top: 50%;background-color: #fff;}
.confirm-wrap{position: relative;width: 100%;height: 100%;}
.confirm-content{text-align: left;color: #000;padding: 0.4rem;border-bottom: 1px solid #e3e3e3;}
.confirm-left{width: 50%;height: 0.88rem;line-height: 0.88rem;color: #777;text-align: center;border-right: 1px solid #e3e3e3;box-sizing: border-box;display: inline-block;}
.confirm-right{width: 50%;height: 0.88rem;line-height: 0.88rem;color: #777;text-align: center;display: inline-block;}
.pop_mask1{width: 100%;height: 100%;background-color: #000;position: fixed;left: 0;top: 0;opacity: 0.5;z-index: 1199;}

.remind{z-index: 1200;border-radius: 10px;position: fixed;width: 60%;left: 20%;top: 50%;background-color: #fff;}
.remind-wrap{position: relative;width: 100%;height: 100%;}
.remind-content{text-align: center;color: #000;padding: 20px 0;border-bottom: 1px solid #e3e3e3;}
.remindBtn{width: 100%;height: 44px;line-height: 44px;color: #448aca;text-align: center;}

.sendform{z-index: 1200;border-radius: 10px;position: fixed;width: 60%;left: 20%;top: 50%;background-color: #fff;}
.sendform-title{height: 44px;line-height: 44px;text-align: center;color: #444;border-bottom: 1px solid #e3e3e3;}
.sendform-content{border-bottom: 1px solid #e3e3e3;}
.sendform textarea{display:block;border: 0;width: 90%;color: #444;margin: 0 auto;padding: 5px 0;overflow: hidden;-webkit-appearance:none; resize:none;}
.sendform-wrap{position: relative;width: 100%;height: 100%;}
.sendform-left{width: 50%;height: 44px;line-height: 44px;color: #777;text-align: center;border-right: 1px solid #e3e3e3;box-sizing: border-box;display: inline-block;}
.sendform-right{width: 50%;height: 44px;line-height: 44px;color: #777;text-align: center;display: inline-block;}

.blueat{border-left: 2px solid #4592ed;background-color: #f5f5f5;padding: 0.05rem 0.10rem;line-height: 0.20rem}
.redtitle{position: relative;padding: 0.12rem 0;font-size: 0.12rem;}
.redtitle:before{content: "";display: block;width: 2px;height: 0.12rem;position: absolute;left: 0;top:0.14rem;border-left: 2px solid #f64d54;}
.emptyimg{width:100%;padding: 50px 0;color: #999;text-align: center;}
.emptyimg img{width: 30%;margin-bottom: 10px;}
._dollar_{color: #4691ee !important;text-decoration: underline !important;}
/*jxc新加*/
.mg15{margin:0.15rem !important;}
.pd15{padding:0.15rem !important;}
/* .fz16{font-size:0.16rem !important;}
.fz15{font-size:0.15rem !important;}
.fz14{font-size:0.14rem !important;}
.fz12{font-size:0.12rem !important;}
.fz10{font-size:0.10rem !important;}*/

/*添加通用line-height*/
.lh12{line-height: 0.12rem!important;} .lh13{line-height: 0.13rem!important;}
.lh14{line-height: 0.14rem!important;} .lh15{line-height: 0.15rem!important;}
.lh16{line-height: 0.16rem!important;} .lh17{line-height: 0.17rem!important;}
.lh18{line-height: 0.18rem!important;} .lh19{line-height: 0.19rem!important;}
.lh20{line-height: 0.20rem!important;} .lh21{line-height: 0.21rem!important;}
.lh22{line-height: 0.22rem!important;} .lh23{line-height: 0.23rem!important;}
.lh24{line-height: 0.24rem!important;} .lh25{line-height: 0.25rem!important;}
.lh26{line-height: 0.26rem!important;} .lh27{line-height: 0.27rem!important;}
.lh28{line-height: 0.28rem!important;} .lh29{line-height: 0.29rem!important;}
.lh30{line-height: 0.30rem!important;} .lh31{line-height: 0.31rem!important;}
.lh32{line-height: 0.32rem!important;} .lh33{line-height: 0.33rem!important;}
.lh34{line-height: 0.34rem!important;} .lh35{line-height: 0.35rem!important;}
.lh36{line-height: 0.36rem!important;} .lh37{line-height: 0.37rem!important;}
.lh38{line-height: 0.38rem!important;} .lh39{line-height: 0.39rem!important;}
.lh40{line-height: 0.40rem!important;}

.cred{color:#e93030 !important;}
.ceee{color: #eee !important;}
.bgfff{background-color: #fff;}
.mlr15{margin:0 0.15rem;}
.hide{display: none;}
/***通用loading状态***/
@font-face {
  font-weight: normal;
  font-style: normal;
  font-family: "weui";
  src: url('data:application/octet-stream;base64,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') format('truetype');
}

@font-face {
  font-family: THSMoneyfont-Medium;
  src: url(//i.thsi.cn/sns/ask/THSMoneyfont-Medium.otf);
}

.weui_loading_toast{ /*添加优化*/
  font-size: .16rem;
}
.weui_mask_transparent {
  position: fixed;
  z-index: 299;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}
.weui_loading_toast .weui_toast_content {
  margin-top: 60%!important;
  font-size: .14rem;
}
.weui_toast {
  position: fixed;
  z-index: 399;
  width: 1.44rem;
  min-height: 1.21rem;
  padding: 0 .1rem;
  top: 1.80rem;
  left: 50%;
  margin-left: -0.84rem;
  background: rgba(40, 40, 40, 0.75);
  text-align: center;
  border-radius: 0.05rem;
  color: #FFFFFF;
  .weui_loading {
    position: absolute;
    width: 0px;
    z-index: 2000000000;
    left: 50%;
    top: 40%!important;
  }
  .weui_loading_leaf {
    position: absolute;
    top: -1px;
    opacity: 0.25;
  }
  .weui_loading_leaf:before {
    content: " ";
    position: absolute;
    width: 0.1rem;
    height: .03rem;
    background: #d1d1d5;
    box-shadow: rgba(0, 0, 0, 0.0980392) 0px 0px 1px;
    border-radius: 1px;
    -webkit-transform-origin: left 50% 0px;
    transform-origin: left 50% 0px;
  }
  .weui_loading_leaf_0 {
    -webkit-animation: opacity-60-25-0-12 1.25s linear infinite;
    animation: opacity-60-25-0-12 1.25s linear infinite;
  }
  .weui_loading_leaf_0:before {
    -webkit-transform: rotate(0deg) translate(0.0792rem, 0px);
    transform: rotate(0deg) translate(0.0792rem, 0px);
  }
  .weui_loading_leaf_1 {
    -webkit-animation: opacity-60-25-1-12 1.25s linear infinite;
    animation: opacity-60-25-1-12 1.25s linear infinite;
  }
  .weui_loading_leaf_1:before {
    -webkit-transform: rotate(30deg) translate(0.0792rem, 0px);
    transform: rotate(30deg) translate(0.0792rem, 0px);
  }
  .weui_loading_leaf_2 {
    -webkit-animation: opacity-60-25-2-12 1.25s linear infinite;
    animation: opacity-60-25-2-12 1.25s linear infinite;
  }
  .weui_loading_leaf_2:before {
    -webkit-transform: rotate(60deg) translate(0.0792rem, 0px);
    transform: rotate(60deg) translate(0.0792rem, 0px);
  }
  .weui_loading_leaf_3 {
    -webkit-animation: opacity-60-25-3-12 1.25s linear infinite;
    animation: opacity-60-25-3-12 1.25s linear infinite;
  }
  .weui_loading_leaf_3:before {
    -webkit-transform: rotate(90deg) translate(0.0792rem, 0px);
    transform: rotate(90deg) translate(0.0792rem, 0px);
  }
  .weui_loading_leaf_4 {
    -webkit-animation: opacity-60-25-4-12 1.25s linear infinite;
    animation: opacity-60-25-4-12 1.25s linear infinite;
  }
  .weui_loading_leaf_4:before {
    -webkit-transform: rotate(120deg) translate(0.0792rem, 0px);
    transform: rotate(120deg) translate(0.0792rem, 0px);
  }
  .weui_loading_leaf_5 {
    -webkit-animation: opacity-60-25-5-12 1.25s linear infinite;
    animation: opacity-60-25-5-12 1.25s linear infinite;
  }
  .weui_loading_leaf_5:before {
    -webkit-transform: rotate(150deg) translate(0.0792rem, 0px);
    transform: rotate(150deg) translate(0.0792rem, 0px);
  }
  .weui_loading_leaf_6 {
    -webkit-animation: opacity-60-25-6-12 1.25s linear infinite;
    animation: opacity-60-25-6-12 1.25s linear infinite;
  }
  .weui_loading_leaf_6:before {
    -webkit-transform: rotate(180deg) translate(0.0792rem, 0px);
    transform: rotate(180deg) translate(0.0792rem, 0px);
  }
  .weui_loading_leaf_7 {
    -webkit-animation: opacity-60-25-7-12 1.25s linear infinite;
    animation: opacity-60-25-7-12 1.25s linear infinite;
  }
  .weui_loading_leaf_7:before {
    -webkit-transform: rotate(210deg) translate(0.0792rem, 0px);
    transform: rotate(210deg) translate(0.0792rem, 0px);
  }
  .weui_loading_leaf_8 {
    -webkit-animation: opacity-60-25-8-12 1.25s linear infinite;
    animation: opacity-60-25-8-12 1.25s linear infinite;
  }
  .weui_loading_leaf_8:before {
    -webkit-transform: rotate(240deg) translate(0.0792rem, 0px);
    transform: rotate(240deg) translate(0.0792rem, 0px);
  }
  .weui_loading_leaf_9 {
    -webkit-animation: opacity-60-25-9-12 1.25s linear infinite;
    animation: opacity-60-25-9-12 1.25s linear infinite;
  }
  .weui_loading_leaf_9:before {
    -webkit-transform: rotate(270deg) translate(0.0792rem, 0px);
    transform: rotate(270deg) translate(0.0792rem, 0px);
  }
  .weui_loading_leaf_10 {
    -webkit-animation: opacity-60-25-10-12 1.25s linear infinite;
    animation: opacity-60-25-10-12 1.25s linear infinite;
  }
  .weui_loading_leaf_10:before {
    -webkit-transform: rotate(300deg) translate(0.0792rem, 0px);
    transform: rotate(300deg) translate(0.0792rem, 0px);
  }
  .weui_loading_leaf_11 {
    -webkit-animation: opacity-60-25-11-12 1.25s linear infinite;
    animation: opacity-60-25-11-12 1.25s linear infinite;
  }
  .weui_loading_leaf_11:before {
    -webkit-transform: rotate(330deg) translate(0.0792rem, 0px);
    transform: rotate(330deg) translate(0.0792rem, 0px);
  }
}

@keyframes opacity-60-25-0-12 {
  0% {
    opacity: 0.25;
  }
  0.01% {
    opacity: 0.25;
  }
  0.02% {
    opacity: 1;
  }
  60.01% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.25;
  }
}
@keyframes opacity-60-25-1-12 {
  0% {
    opacity: 0.25;
  }
  8.34333% {
    opacity: 0.25;
  }
  8.35333% {
    opacity: 1;
  }
  68.3433% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.25;
  }
}
@keyframes opacity-60-25-2-12 {
  0% {
    opacity: 0.25;
  }
  16.6767% {
    opacity: 0.25;
  }
  16.6867% {
    opacity: 1;
  }
  76.6767% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.25;
  }
}
@keyframes opacity-60-25-3-12 {
  0% {
    opacity: 0.25;
  }
  25.01% {
    opacity: 0.25;
  }
  25.02% {
    opacity: 1;
  }
  85.01% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.25;
  }
}
@keyframes opacity-60-25-4-12 {
  0% {
    opacity: 0.25;
  }
  33.3433% {
    opacity: 0.25;
  }
  33.3533% {
    opacity: 1;
  }
  93.3433% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.25;
  }
}
@keyframes opacity-60-25-5-12 {
  0% {
    opacity: 0.270958333333333;
  }
  41.6767% {
    opacity: 0.25;
  }
  41.6867% {
    opacity: 1;
  }
  1.67667% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.270958333333333;
  }
}
@keyframes opacity-60-25-6-12 {
  0% {
    opacity: 0.375125;
  }
  50.01% {
    opacity: 0.25;
  }
  50.02% {
    opacity: 1;
  }
  10.01% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.375125;
  }
}
@keyframes opacity-60-25-7-12 {
  0% {
    opacity: 0.479291666666667;
  }
  58.3433% {
    opacity: 0.25;
  }
  58.3533% {
    opacity: 1;
  }
  18.3433% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.479291666666667;
  }
}
@keyframes opacity-60-25-8-12 {
  0% {
    opacity: 0.583458333333333;
  }
  66.6767% {
    opacity: 0.25;
  }
  66.6867% {
    opacity: 1;
  }
  26.6767% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.583458333333333;
  }
}
@keyframes opacity-60-25-9-12 {
  0% {
    opacity: 0.687625;
  }
  75.01% {
    opacity: 0.25;
  }
  75.02% {
    opacity: 1;
  }
  35.01% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.687625;
  }
}
@keyframes opacity-60-25-10-12 {
  0% {
    opacity: 0.791791666666667;
  }
  83.3433% {
    opacity: 0.25;
  }
  83.3533% {
    opacity: 1;
  }
  43.3433% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.791791666666667;
  }
}
@keyframes opacity-60-25-11-12 {
  0% {
    opacity: 0.895958333333333;
  }
  91.6767% {
    opacity: 0.25;
  }
  91.6867% {
    opacity: 1;
  }
  51.6767% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.895958333333333;
  }
}
.myadshow {
  position: fixed!important;
  width: 100%;
  min-height: 60px;
  max-height: 0.6rem;
  top: 0;
  left: 0;
  z-index: 9999;
}
.myadshow .myadclose {
  position: absolute;
  right: 0;
  top: 0;
  width: 10%;
  height: 100%;
  z-index: 100;
}
#thsDownload .myadclose{
  width: 32px;
  background: url(//i.thsi.cn/images/mhexin/v3/close.png) 50% 50% no-repeat;
  background-size: 16px 16px;
}
.myadshow img{
  border:0;
  width: 100%;
  display: block;
}
.mytest{
  display: block;
}

/*手机新加图标*/
.new-icons-zan{
  display: inline-block;
  position: relative;
  width: 0.12rem;
  height: 0.13rem;
  background: url(//i.thsi.cn/sns/circle/wapcircle/feed/new-icons-zan.png) no-repeat;
  background-size: 100% 100%;
}
.new-icons-zanActive{
  display: inline-block;
  position: relative;
  width: 0.12rem;
  height: 0.13rem;
  background: url(//i.thsi.cn/sns/circle/wapcircle/feed/new-icons-zanActive.png) no-repeat;
  background-size: 100% 100%;
}
.new-icons-eye{
  display: inline-block;
  position: relative;
  width: 0.15rem;
  height: 0.1rem;
  background: url(//i.thsi.cn/sns/circle/wapcircle/feed/new-icons-eye.png) no-repeat;
  background-size: 100% 100%;
}
.new-icons-info{
  display: inline-block;
  position: relative;
  width: 13px;
  height: 12px;
  background: url(//i.thsi.cn/sns/circle/wapcircle/feed/new-icons-info.png) no-repeat;
  background-size: 100% 100%;
}
.new-icons-info2{
  display: inline-block;
  position: relative;
  width: 0.14rem;
  height: 0.12rem;
  background: url(//i.thsi.cn/sns/circle/wapcircle/feed/new-icons-info2.png) no-repeat;
  background-size: 100% 100%;
}
.new-icons-forward{
  display: inline-block;
  position: relative;
  width: 14px;
  height: 12px;
  background: url(//i.thsi.cn/sns/circle/wapcircle/feed/new-icons-forward.png) no-repeat;
  background-size: 100% 100%;
}

.reply-text .isAdviser-icon:last-child {
  margin-left: 0.03rem;
}
.isAdmin-icon{
  width: 0.14rem;
  height: 0.12rem;
  background: url(//i.thsi.cn/sns/circle/wapcircle/addV/isAdmin.png) no-repeat;
  background-size: 100% 100%;
}
.isAdviser-icon{
  width: 0.1rem !important;
  height: 0.1rem !important;
  background: url(//i.thsi.cn/sns/circle/wapcircle/addV/isAdviser.png) no-repeat !important;
  background-size: 100% 100% !important;
}
.isAdviser-icon-circle{
  display: inline-block;
  width: .17rem;
  height: .17rem;
  background: url(//i.thsi.cn/sns/circle/wapcircle/addV/V-isAdviser-new.png) no-repeat;
  background-position: 0 0;
  background-size: 0.16rem;
  margin: 0 0 0 .04rem;
  margin-left: 2px;
  margin-right: 0.01rem;
  position: relative;
}
.isV-icon {
  width: 0.12rem;
  height: 0.12rem;
  background: url(//i.thsi.cn/sns/circle/wapcircle/img2/newlive/wap-v.png);
  background-size: 100% 100%;
  vertical-align: middle;
}
.icon-relb2{
  position: relative;
  top: -2px;
}
.icon-relb1{
  position: relative;
  top: -1px;
}
.isCharge{
  width: 0.09rem;
  height: 0.11rem;
  background: url(//i.thsi.cn/sns/circle/wapcircle/addV/isCharge.png) no-repeat;
  background-size: 100% 100%;
}
.isExcellent-icon{
  width: 0.1rem;
  height: 0.1rem;
  background: url(//i.thsi.cn/sns/circle/wapcircle/addV/isExcellent.png) no-repeat;
  background-size: 100% 100%;
}
.isExcellent-icon-circle{
  display: inline-block;
  width: .17rem;
  height: .17rem;
  background: url(//i.thsi.cn/sns/circle/wapcircle/addV/V-isExcellent-new.png) no-repeat;
  background-position: 0 0;
  background-size: 0.16rem;
  margin: 0 0 0 .04rem;
  margin-left: 2px;
  margin-right: 0.01rem;
  // vertical-align: middle;
  position: relative;
  top: -0.01rem;
}
.isSign{
  width: 0.12rem;
  height: 0.11rem;
  background: url(//i.thsi.cn/sns/circle/wapcircle/addV/isSign.png) no-repeat;
  background-size: 100% 100%;
}
//vip背景图
.vip-icon{
  background-image: url('//i.thsi.cn/sns/circle/wap/vip.png')!important;
}
//认证身份
.circleVip-intro-box{
  // width: 2.70rem;
  background-color: #fff;
  border-radius: 0.12rem;
  position: absolute;
  padding: 0.16rem 0.10rem;
  z-index: 10001;
  box-shadow: 0 0 8px #dedede;
  .lightbluetext{
    color: #48a4d5;
  }
  .yellowtext{
    color: #fea31e;
  }
  .vat{
    vertical-align: top;
  }
  .vip-intro{
    .vip-icon{
      display: inline-block;
      width: 0.16rem;
      height: 0.16rem;
      background-image: url('//i.thsi.cn/sns/circle/wapcircle/newgroup/homepage/vip.png');
      background-size: 100% 100%;
      vertical-align: middle;
      margin-right: 0.08rem;
      margin-top: -0.01rem;
      margin-top: 0.02rem;
    }
    .vip-auth{
      .rem14();
      .c666();
      .ellipsis{
        display: inline-block;
        max-width: 0.90rem;
      }
      @media screen and (max-width: 320px) {
        .ellipsis{
          max-width: 0.80rem;
        }
      }
    }
    .vip-arrow{
      width: 0;
      height: 0;
      border-left: 0.05rem solid transparent;
      border-right: 0.05rem solid transparent;
      position: absolute;
      left: 50px;
      &.down{
        border-top: 0.07rem solid #fff;
        bottom: -0.05rem;
      }
      &.up{
        border-bottom: 0.07rem solid #fff;
        top: -0.05rem;
      }
    }

    .vip-more{
      display: inline-block;
      width: 0.08rem;
      height: 0.08rem;
      border-right: 1px solid #ccc;
      border-top: 1px solid #ccc;
      transform:rotate(45deg);
      vertical-align: middle;
      margin-left: 0.02rem;
    }
  }
  .isExcellent-icon,.isAdviser-icon{
    position: relative;
    width: 0.12rem;
    height: 0.12rem;
    margin-right: 0.05rem;
    vertical-align: middle;
    display: inline-block;
    top: 0.05rem;
  }
}

/* 风险提示 */
.hostTipAlert,.attenAlert{
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  background-color: rgba(0,0,0,0.75);
  display: none;
}
.attenAlert{
  background-color: rgba(0,0,0,0);
}
.attenAlert .canvas{
  position: fixed;
  width: 100%;
  height: 10000px;
  top: 0;
  background-color: rgba(0,0,0,0.75);
}
.hostTipAlertWindow,.attenAlertWindow  {
  position: fixed;
  top: 20%;
  left: 18%;
  width: 64%;
  z-index: 1100;
  background-color: #fff;
  border-radius: 0.05rem;
  -webkit-border-radius: 0.05rem;
  padding-bottom: 0.10rem;
}
.hostTipAlertWindow{
  padding-bottom: .15rem;
}
.hostTipAlertTitle ,.attenAlertTitle{
  text-align: center;
  padding: 0.05rem 0;
  background-color: #4691ee;
  color: #fff;
  font-size: 0.14rem;
  border-top-left-radius: 0.05rem;
  -webkit-border-top-left-radius: 0.05rem;
  border-top-right-radius: 0.05rem;
  -webkit-border-top-right-radius: 0.05rem;
}
.hostTipAlertTitle{
  background-color: #fff;
  font-size: .16rem;
  color: #323232;
  line-height: 1.5;
  padding-top: 0.15rem;
  padding-bottom: .09rem;
}
.hostTipAlertCon ,.attenAlertCon{
  padding: 0.08rem;
  font-size: 0.14rem;
  text-align: justify;
  color: #323232;
}
.hostTipAlertCon{
  font-size: .14rem;
  line-height: .22rem;
  padding: 0 0.14rem 0.1rem 0.14rem;
}
.hostTipAlertProtocol > span:first-child {
  display: inline-block;
  width: 0.12rem;
  height: 0.12rem;
  border: 1px solid #5399ef;
  border-radius: 0.12rem;
  vertical-align: middle;
  margin-right: 0.05rem;
  margin-left: 0.10rem;
}
.hostTipAlertProtocol > span:first-child > i {
  display: inline-block;
  width: 0.07rem;
  height: 0.05rem;
  border-left: 1px solid #5399ef;
  border-bottom: 1px solid #5399ef;
  transform: rotate(-45deg);
  -webkit-transform: rotate(-45deg);
  vertical-align: top;
  position: relative;
  top: 0.01rem;
  left: 0.02rem;
}
.hostTipAlertProtocol > span {
  font-size: 0.14rem;
  color: #323232;
}
.hostTipAlertProtocol a {
  font-size: 0.14rem;
  color: #5399ef;
}
.hostTipAlertBtn ,.attenAlertBtn{
  text-align: center;
  margin-top: 0.10rem;
}
.hostTipAlertBtn > span ,.attenAlertBtn > span{
  display: inline-block;
  font-size: 0.16rem;
  color: #afafaf;
  padding: 0.05rem 0;
  width: 38%;
  border: 1px solid #afafaf;
  border-radius: 0.50rem;
  -webkit-border-radius: 0.50rem;
}
.hostTipAlertBtn > span:first-child ,.attenAlertBtn > span:first-child{
  margin-right: 6%;
}
.hostTipAlertBtn > span.hostTipAlertBtnYes ,.attenAlertBtn > span.attenAlertBtnYes{
  background-color: #5399ef;
  border-color: #5399ef;
  color: #fff;
}
.hostTipAlertBtn > span.hostTipAlertBtnYes.hostTipBtnNew{
  width: 1.6rem;
  height: .4rem;
  margin: 7px 0 0 0;
  padding: 0;
  line-height: .4rem;
}

//风险提示合规处理 @REMOVE
.brief-risk-wrap{
  width: 80%;
  height: 60%;
  background: #fff;
  position: absolute;
  margin-left: 10%;
  top: 50%;
  transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  padding: 0.15rem;
  border-radius: 3px;
  .brief-risk-body{
    width: 100%;
    overflow: scroll;
  }
  .brief-risk-btn{
    display: inline-block;
    padding: 0 0.1rem;
    border-radius: 3px;
    height: 0.28rem;
    line-height: 0.28rem;
    font-size: 0.14rem;
    margin-top: 0.15rem;
    color: #999;
    background: #e7e7e7;
    &.active{
      color: #fff;
      background: #4691ee;
    }
  }
  .brief-risk-body{
    text-indent: 24px;
    text-align: justify;
    line-height: 0.18rem;
  }
}

/*risk end*/

// app外部图片放大 微信分享
.swiper-mainWrap{
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1200;
  background: rgba(0, 0, 0, 0.5);
  .swiper-container{
    width: 100%;
    height: 100%;
  }
  .swiper-close{
    width: 30px;
    height: 30px;
    position: fixed;
    right: 10px;
    top: 10px;
    z-index: 1201;
    i{
      width: 30px;
      height: 2px;
      display: inline-block;
      position: absolute;
      top: 15px;
      left: 0;
      background: #e5e5e5;
      -webkit-transform:rotate(45deg);
      transform:rotate(45deg);
      &:first-child{
        -webkit-transform:rotate(-45deg);
        transform:rotate(-45deg);
      }
    }
  }
}
.weixin-share-wrap{
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,.5);
  z-index: 999;
  img{
    width: 100%;
    height: 100%;
  }
}
/* wap视频结构通用样式 */
.wap-video-con{
  position: relative;
  overflow: hidden;
  .wap-video-cover{
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    // background: #333;
    background: url(//i.thsi.cn/sns/circle/wapcircle/img2/wap-video-cover.jpg) center top no-repeat;
    background-size: cover;
    overflow: hidden;
    img{
      width: 100%;
      height: 100%;
    }
    .wap-video-icon{
      display: block;
      width: 0.52rem;
      height: 0.52rem;
      background: url(//i.thsi.cn/sns/circle/img3/video-play.png) no-repeat;
      background-size: 100% 100%;
      position: absolute;
      top: 50%;
      left: 50%;
      margin-top: -0.26rem;
      margin-left: -0.26rem;
      z-index: 19;
    }
  }
  .wap-video,#wap-video{
    display: none;
    position: absolute;
    width: 100%;
    height: 100%;
    background: #000;
  }
  .wap-video{
    display: none;
    position: absolute;
    width: 100%;
    height: 100%;
    background: #000;
  }
  .wap-video-word{
    position: absolute;
    top: 50%;
    width: 100%;
    line-height: 0.16rem;
    margin-top: 0.4rem;
    text-align: center;
    color: #999;
  }
}
//浮层式的样式
.wap-video-fix{
  width: 100%;
  height: 100%;
  z-index: 100;
  display: none;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1199;
  .wap-video-bg{
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #000;
    opacity: 0.9;
  }
  .wap-video{
    position: absolute;
    width: 100%;
    top: 50%;
    height: 2.16rem;
    background: #000;
    transform: translateY(-50%);
  }
}
//通用错误提示样式
.error-tips-common{
  font-size: 14px;
  color: #fc512a;
  background: #fedcd3;
  text-align: center;
  height: 34px;
  line-height: 34px;
  position: absolute;
  left: 0;
  right: 0;
  z-index: 111;
}

/*
  流数据加载使用的loading方式
  使用TxtLoading方法
*/

.txtLoading{
  font-size: .14rem;
  color: #999;
  text-align: center;
  line-height: .50rem;
  .loadHisOver{
    width: 100%;
    line-height: 0.32rem;
    height: 0.32rem;
    text-align: center;
    color: #999;
    font-size: 0.12rem;
    margin-top: 0.05rem;
  }
}

/*公告弹框*/
.notice-remindinner{
  padding: 5px 25px 0 25px;
  .reminderinner-title{
    line-height: 28px;
  }
  .reminderinner-content{
    line-height: 26px;
    text-align: left;
  }
  .remindBtn{
    color: #323232;
  }
}

/*表情 图片大小44px 需要设置 22px*/
.snsface,.emojiface{
  width: 22px!important;
  height: 22px!important;
}
.stickeremoji{
  width: 0.7rem !important;
  height: 0.7rem !important;
}
.fontinit .snsface{
  width: 0.22rem!important;
  height: 0.22rem!important;
}
.fontinit .emojiface{
  width: 0.37rem!important;
  height: 0.37rem!important;
  vertical-align: middle;
}

[v-cloak]{display: none;}

/* 反馈样式 */
.feedback-wrap-rem{
  position: fixed;
  right: 0.2rem;
  bottom: 0.65rem;
  width: 0.28rem;
  height: 0.28rem;
  color: #fff;
  font-size: 0.14rem;
  text-align: center;
  line-height: 0.28rem;
  z-index: 10;
  /*问答反馈样式*/
  &.askchat{
    right: 0.15rem;
    bottom: 1.05rem;
    width: 0.25rem;
    height: 0.25rem;
    line-height: 0.25rem;
  }
  .fb-body{
    background: #febe61;
    border-radius: 50%;
  }
  .fb-btn-box{
    position: absolute;
    right: 0.33rem;
    top: -0.48rem;
    padding: 0.16rem 0;
    box-shadow: 0 0 10px 3px rgba(0, 0, 0, 0.1);
    background: #fff;
    li{
      width: 1rem;
      height: 0.34rem;
      line-height: 0.34rem;
      background: #fff;
      &.active{
        background: #e5e5e5;
      }
      a:active{
        background: #e5e5e5;
      }
    }
  }
}

.feedback-wrap-px{
  position: fixed;
  right: 20px;
  bottom: 65px;
  width: 28px;
  height: 28px;
  color: #fff;
  font-size: 14px;
  text-align: center;
  line-height: 28px;
  z-index: 10;
  .fb-body{
    background: #febe61;
    border-radius: 50%;
  }
  .fb-btn-box{
    position: absolute;
    right: 33px;
    top: -48px;
    padding: 16px 0;
    box-shadow: 0 0 10px 3px rgba(0, 0, 0, 0.1);
    background: #fff;
    li{
      width: 100px;
      height: 34px;
      line-height: 34px;
      background: #fff;
      &.active{
        background: #e5e5e5;
      }
      a:active{
        background: #e5e5e5;
      }
    }
  }
}

@media only screen and (device-width:375px) and (device-height:812px) and (-webkit-device-pixel-ratio:3) {
  .Gfixed{
    padding-bottom: .34rem !important;
  }
  .Gfixedpx{
    padding-bottom: 34px !important;
  }
  .Gfixed39{
    padding-bottom: .39rem !important;
  }
  .Gfixed39px{
    padding-bottom: 39px !important;
  }
  .Gfixed44{
    padding-bottom: .44rem !important;
  }
  .Gfixed44px{
    padding-bottom: 44px !important;
  }
  /*问答模块 iPhoneX 兼容*/
  .border-gray{
    border:.01rem solid #eee!important;
  }
  .inviteList-btn-box{
    border: .01rem solid #e93030!important;
  }
}
/* margin padding less 计算 start */
.template-mp(20);
.template-mp(@n, @i: 0) when (@i <= @n){
  @k: @i * 5;
  .p@{k}rem { padding:  1rem / 100 * @k !important; }
  .pl@{k}rem { padding-left:  1rem / 100 * @k !important; }
  .pr@{k}rem { padding-right:  1rem / 100 * @k !important; }
  .pt@{k}rem { padding-top:  1rem / 100 * @k !important; }
  .pb@{k}rem { padding-bottom:  1rem / 100 * @k !important; }

  .m@{k}rem { margin:  1rem / 100 * @k !important; }
  .ml@{k}rem { margin-left:  1rem / 100 * @k !important; }
  .mr@{k}rem { margin-right:  1rem / 100 * @k !important; }
  .mt@{k}rem { margin-top:  1rem / 100 * @k !important; }
  .mb@{k}rem { margin-bottom:  1rem / 100 * @k !important; }

  .template-mp(@n, (@i + 1));
}
/* margin padding less 计算 end */

/* font-size less 计算 start */
.template-fs-fz(20);
.template-fs-fz(@n, @i: 5) when (@i <= @n){
  @k: @i * 2;
  .rem@{k} { font-size: 1rem / 100 * @k !important; }
  .fs@{k} { font-size: 1px * @k !important; }
  .fz@{k} { font-size: 1px * @k !important; }
  .lh@{k} { line-height: 1rem / 100 * @k !important; }
  .template-fs-fz(@n, (@i + 1));
}

/* font-size less 计算 end */

/* color 计算 start */
.template-c(9);
.template-c(@n, @i: 1) when (@i <= @n){
  .c@{i}@{i}@{i}{ color: #111 * @i !important; }

  .template-c(@n, (@i + 1));
}
/* color 计算 end */


._dollar_nodecoration_{
  text-decoration: none!important;
}
.brr50{
  border-radius: 50%;
}
.brr4{
  border-radius: 0.04rem;
}

/*
 * 时间：2017-12-20 17:24
 * 功能:通用title
 * 地址:http://t.10jqka.com.cn/m/package/pkgItem/
*/
.item-title{
  color: #323232;
  font-size: .18rem;
  line-height: .2rem;
  height: .19rem;
  padding-left: 0.08rem;
  position: relative;
  .bl{
    width: 0.03rem;
    background-color: #e92f30;
    height: .17rem;
    line-height: .17rem;
    position: absolute;
    top: 50%;
    margin-top: -0.085rem;
    left: 0;
  }
}
.noborder{border: 0 !important;}
.dt{
  display: table;
}

//三端荣耀标识样式
.wapadviser-wrap{
  position: fixed;
  padding: 0.08rem 0.12rem;
  border-radius: 4px;
  box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.1);
  z-index: 20;
  .wapadviser-arrow{
    position: absolute;
    display: block;
    width: 0;
    height: 0;
    &.top{
      top: -6px;
      border-left: solid 8px transparent;
      border-right: solid 8px transparent;
      border-top: none;
      border-bottom: solid 8px #fff;
    }
    &.bottom{
      bottom: -6px;
      border-left: solid 8px transparent;
      border-right: solid 8px transparent;
      border-bottom: none;
      border-top: solid 8px #fff;
    }
  }
}
.curd{cursor: default !important;}
.curp{cursor: pointer !important;}

/*
 * 时间：2018-1-25 15:02:36
 * 功能: initFontSize(1) 页面风险测评提示通用样式
 * 地址:http://t.10jqka.com.cn/m/group/adviserPoint/?stockcodes=1A0001
*/
.newHostTipAlert{
  .brief-risk-wrap{
    font-size: 0.14rem!important;
    padding: 0;
    border-radius: 0.04rem;
  }
  .brief-risk-title{
    font-size: 0.18rem!important;
    padding: 0.26rem;
    padding-bottom: 0;
  }
  .brief-risk-body{
    text-indent: 0.24rem;
    margin: 0 0.26rem;
    width: 83%;
  }
  .brief-risk-btn{
    border-radius: 0.03rem;
    height: 0.42rem;
    line-height: 0.42rem;
    color: #999;
    background: #fff;
    width: 100%;
    margin: 0;
    margin-top: 0.2rem;
    padding: 0;
    &.active{
      background: #fff;
      color:#e93030;
    }
  }
}
/*push 弹窗*/
#pushTopicDialog-on,#pushTopicDialog-off{
  z-index: 1200;
  border-radius: 0.04rem;
  position: fixed;
  width: 2.8rem;
  margin-left: -1.4rem;
  left: 50%;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  background-color: #fff;
  padding-top: 0.08rem;
  .pushTopicDialog-on-header,.pushTopicDialog-off-header{
    font-size: 0.18rem;
    font-weight: bold;
    text-align: center;
    line-height: 0.48rem;
  }
  .pushTopicDialog-on-center,.pushTopicDialog-off-center{
    font-size: 0.16rem;
    padding: 0 0.24rem 0.25rem 0.24rem;
    border-bottom: 1px solid #ddd;
  }
  .pushTopicDialog-on-footer,.pushTopicDialog-off-footer{
    text-align: center;
    font-size: 0.17rem;
    line-height: 0.44rem;
  }
  .pushTopicDialog-on-footer-no{
    float: left;
    width: 1.39rem;
    text-align: center;
    font-size: 0.17rem;
    line-height: 0.44rem;
    border-right: 0.01rem solid #ddd;
    color: #999;
  }
  .pushTopicDialog-on-footer-yes{
    float: left;
    width: 1.39rem;
    text-align: center;
    font-size: 0.17rem;
    line-height: 0.44rem;
  }
}
.pushTopicDialogMask-off,.pushTopicDialogMask-on{
  width: 100%;
  height: 100%;
  background-color: #000;
  position: fixed;
  left: 0;
  top: 0;
  opacity: 0.5;
  z-index: 1199;
}
.night{
  #pushTopicDialog-on,#pushTopicDialog-off{
    background-color: #252528;
    color: #d2d2d3;
    .pushTopicDialog-on-center,.pushTopicDialog-off-center{
      border-bottom-color: #313131;
    }
    .pushTopicDialog-on-footer-no{
      border-right-color: #313131;
    }
  }
}
/*星星*/
.rating-body-box{
  text-align: center;
}
.rating-body-box .rating-item{
  display: inline-block;
  width: .12rem;
  height: .13rem;
  background: url(//i.thsi.cn/sns/circle/wapcircle/img2/askChat/stargray.png) no-repeat 100% 100%;
  background-size: cover;
  margin: 0 .09rem;
}
.rating-body-box .rating-item.active{
  display: inline-block;
  width: .12rem;
  height: .13rem;
  background: url(//i.thsi.cn/sns/circle/wapcircle/img2/askChat/staractive.png) no-repeat 100% 100%;
  background-size: cover;
  margin: 0 .09rem;
}
.rating-body-box .rating-item.half{
  display: inline-block;
  width: .12rem;
  height: .13rem;
  background: url(//i.thsi.cn/sns/circle/wapcircle/img2/askChat/starhalf.png) no-repeat 100% 100%;
  background-size: cover;
  margin: 0 .09rem;
}
