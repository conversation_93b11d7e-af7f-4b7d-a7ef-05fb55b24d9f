@import './reset.less';
@orange:#ff801a;
@red:#e93030;
@darkred: #96271d;
@lightred: #ffa1a1;
@yellow:#ffab32;
@blue:#4691ee;
@lightblue: #5856d6;
@black: #000;
@yellow:#ffb400;
@green:#009900;
@ff: #ffffff;
@f2:#f2f2f2;
@f5:#f5f5f5;
@e5:#e5e5e5;
@ee:#eee;
@e3:#e3e3e3;
@66: #666666;
@99: #999;
@cc: #ccc;
@31:#313131;
@32:#323232;
@33:#333;
@f0:#f0f0f0;
@dd:#ddd;
@d2:#d2d2d2;
@31:#313131;
@25:#252525;
@20:#202022;
@8e:#8e8e8e;
@d2:#d2d2d2;
@80:#808080;
@a7: #a7a7a7;
@2a: #2a2a2a;
@a9: #a9a9a9;
@nightbg:#252528;
@nightred:#fd4332;
@nightgreen:#30a431;
@nightblue: #1e3c5b;
@nightred-p5: rgba(253,67,50,0.05);
@nightred-p70: rgba(253,67,50,0.7);
@red-p5: rgba(233,48,48,0.05);
@red-p70: rgba(233,48,48,0.7);
@nightfont: #d2d2d3;
@ed:#ededed;
@androidnightbg:#171616;

@gsjlred:#f64d54;
@gsjlblue:#3b6299;
@gsjlbluenew:#1da1f2;

@regular: 'PingFang-SC-Regular';

.p010(){padding: 0 .10rem;}
.p020(){padding: 0 .20rem;}

.mt7(){margin-top: 0.07rem!important;}
.mb7(){margin-bottom: 0.07rem!important;}


.mt5(){margin-top: 0.05rem!important;}
.ml5(){margin-left: 0.05rem!important;}
.mr5(){margin-right: 0.05rem!important;}
.mb5(){margin-bottom: 0.05rem!important;}
.mt10(){margin-top: 0.10rem!important;}
.ml10(){margin-left: 0.10rem!important;}
.mr10(){margin-right: 0.10rem!important;}
.mb10(){margin-bottom: 0.10rem!important;}
.mt15(){margin-top: 0.15rem!important;}
.ml15(){margin-left: 0.15rem!important;}
.mr15(){margin-right: 0.15rem!important;}
.mb15(){margin-bottom: 0.15rem!important;}
.mt20(){margin-top: 0.20rem!important;}
.ml20(){margin-left: 0.20rem!important;}
.mr20(){margin-right: 0.20rem!important;}
.mb20(){margin-bottom: 0.20rem!important;}
.mt30(){margin-top: 0.30rem!important;}
.ml30(){margin-left: 0.30rem!important;}
.mr30(){margin-right: 0.30rem!important;}
.mb30(){margin-bottom: 0.30rem!important;}
.mt40(){margin-top: 0.40rem!important;}
.ml40(){margin-left: 0.40rem!important;}
.mr40(){margin-right: 0.40rem!important;}
.mb40(){margin-bottom: 0.40rem!important;}


.rem8(){font-size: .08rem !important;}
.rem10(){font-size: .10rem !important;}
.rem12(){font-size: .12rem !important;}
.rem13(){font-size: .13rem !important;}
.rem14(){font-size: .14rem !important;}
.rem15(){font-size: .15rem !important;}
.rem16(){font-size: .16rem !important;}
.rem18(){font-size: .18rem !important;}
.rem24(){font-size: .24rem !important;}
.rem28(){font-size: .28rem !important;}
.rem32(){font-size: .32rem !important;}

.lh8(){line-height: .08rem !important;}
.lh10(){line-height: .10rem !important;}
.lh12(){line-height: .12rem !important;}
.lh14(){line-height: .14rem !important;}
.lh16(){line-height: .16rem !important;}
.lh18(){line-height: .18rem !important;}
.lh24(){line-height: .24rem !important;}
.lh28(){line-height: .28rem !important;}
.lh36(){line-height: .36rem !important;}

.pr5(){padding-right: 0.05rem;}
.pl5(){padding-left: 0.05rem;}
.pt5(){padding-top: 0.05rem;}
.pb5(){padding-bottom: 0.05rem;}
.pr10(){padding-right: 0.10rem;}
.pl10(){padding-left: 0.10rem;}
.pt10(){padding-top: 0.10rem;}
.pb10(){padding-bottom: 0.10rem;}
.pr20(){padding-right: 0.20rem;}
.pl20(){padding-left: 0.20rem;}
.pt20(){padding-top: 0.20rem;}
.pb20(){padding-bottom: 0.20rem;}
.pr30(){padding-right: 0.30rem;}
.pl30(){padding-left: 0.30rem;}
.pt30(){padding-top: 0.30rem;}
.pb30(){padding-bottom: 0.30rem;}
.pr40(){padding-right: 0.40rem;}
.pl40(){padding-left: 0.40rem;}
.pt40(){padding-top: 0.40rem;}
.pb40(){padding-bottom: 0.40rem;}
.pr50(){padding-right: 0.50rem;}
.pl50(){padding-left: 0.50rem;}
.pt50(){padding-top: 0.50rem;}
.pb50(){padding-bottom: 0.50rem;}
.pr60(){padding-right: 0.60rem;}
.pl60(){padding-left: 0.60rem;}
.pt60(){padding-top: 0.60rem;}
.pb60(){padding-bottom: 0.60rem;}
.pr70(){padding-right: 0.70rem;}
.pl70(){padding-left: 0.70rem;}
.pt70(){padding-top: 0.70rem;}
.pb70(){padding-bottom: 0.70rem;}
.pr80(){padding-right: 0.80rem;}
.pl80(){padding-left: 0.80rem;}
.pt80(){padding-top: 0.80rem;}
.pb80(){padding-bottom: 0.80rem;}
.p016(){padding: 0 0.16rem;}
.wordbreak(){word-break: break-word;word-wrap:break-word;}

.cfff(){color: #fff !important;}
.c333(){color: #333 !important;}
.c666(){color: #666 !important;}
.c777(){color: #777 !important;}
.c999(){color: #999 !important;}
.c8e(){color: #8e8e8e !important;}
.ofh(){overflow: hidden;}
.tac(){text-align: center;}
.tar(){text-align: right;}
.tal(){text-align: left;}
.taj(){text-align: justify;}
.center(){margin: 0 auto;}
.bluetext(){color: #4691ee !important;}
.redtext(){color: #e93030 !important;}
.bluebg(){background-color: #4691ee !important;}
.yellowbg(){background-color: #ffab32 !important;}
.graybg(){background-color: #e7e7e7 !important;}
.redbg(){background-color: #c73138 !important;}
.whitebg(){background-color: #fff !important;}
/**单行溢出*/
.ellipsis(){overflow: hidden; white-space: nowrap; text-overflow: ellipsis; }
.wdwrap(){word-break: break-word;word-wrap:break-word;}
.rowflex(){display: flex;display: -webkit-flex;flex-direction: row;display: -webkit-box;-webkit-box-orient:horizontal;}
.columnflex(){display: flex;display: -webkit-flex;flex-direction: column;display: -webkit-box;-webkit-box-orient:vertical;}
.flex1(){-webkit-box-flex:1;flex:1;-webkit-flex:1;}
.fwb(){font-weight: bold;}
/**多行溢出,@clamp:显示的行数*/
.multi-ellipsis(@clamp){
  overflow: hidden;
  text-overflow:ellipsis;
  display: -webkit-box;
  box-sizing:border-box;
  -webkit-box-sizing:border-box;
  -webkit-line-clamp:@clamp;
  -webkit-box-orient:vertical;
}
.rowflex(){display: flex;display: -webkit-flex;flex-direction: row;display: -webkit-box;-webkit-box-orient:horizontal;}
.columnflex(){display: flex;display: -webkit-flex;flex-direction: column;display: -webkit-box;-webkit-box-orient:vertical;}
.flex1(){-webkit-box-flex:1;flex:1;-webkit-flex:1;}
.media(){
  html,body{
    width: 100%;
    height: 100%;
  }
  @media screen and (min-width: 320px) {
    html{
      font-size: 85.3px;
    }
  }
  @media screen and (min-width: 360px) {
    html{
      font-size: 96px;
    }
  }
  @media screen and (min-width: 400px) {
    html{
      font-size: 106.7px;
    }
  }
  @media screen and (min-width: 480px) {
    html{
      font-size: 128px;
    }
  }
  @media screen and (min-width: 520px) {
    html{
      font-size: 138.7px;
    }
  }
  @media screen and (min-width: 640px) {
    html{
      font-size: 170.7px;
    }
  }
}


.zanicon{
  width: 11px;
  height: 11px;
  display: inline-block;
  background-size: 100% 100%;
  background-image: url(//i.thsi.cn/sns/circle/wapcircle/mlive/beforethx.png);
  &.zaned{
    background-image: url(//i.thsi.cn/sns/circle/wapcircle/mlive/thx.png);
  }
}
.zaned .zanicon{
  background-image: url(//i.thsi.cn/sns/circle/wapcircle/mlive/thx.2017.png);
}
.z1200{
  z-index: 1200 !important;
}

/**夜间模式**/
.nightbg(){background-color: @nightbg !important;}
