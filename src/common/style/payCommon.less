@charset "utf-8";
@import "./base.less";
/* 支付模块 *2rem通用 */
.payMask {
    background-color: rgba(0,0,0,0.55);
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index:25;
}
.payModel {
    position: fixed;
    top: 100%;
    left: 0;
    width: 100%;
    background-color: #fff;
    z-index: 30;
}
.payMain, .payWait, .payTimeout, .paySure, .pay-mode-list, .pay-protocol-list, .pay-info-list {
    background-color: #fff;
    padding: 0.1*2rem;
    font-size: 0.14*2rem;
    color: #323232;
}
.payMainDetail {
    padding-bottom: 0.1*2rem;
    border-bottom: 1px solid #e5e5e5;
}
.payMainDetail > span:first-child {
    width: 100%;
    display: inline-block;
}
//.payMainDetail > span:last-child {
//    float: right;
//    color: #cccccc;
//    line-height: 0.21*2rem;
//}
.payMainDetail > span:last-child ins {
    display: inline-block;
    text-decoration: none;
    width: 0.15*2rem;
    height: 0.15*2rem;
    background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABsAAAAcCAIAAAAfs1O6AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyJpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMy1jMDExIDY2LjE0NTY2MSwgMjAxMi8wMi8wNi0xNDo1NjoyNyAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNiAoV2luZG93cykiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6ODg0QzkyNjkzN0JEMTFFNkIzOUNCOTYzRjU4OURDQTkiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6ODg0QzkyNkEzN0JEMTFFNkIzOUNCOTYzRjU4OURDQTkiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDo4ODRDOTI2NzM3QkQxMUU2QjM5Q0I5NjNGNTg5RENBOSIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDo4ODRDOTI2ODM3QkQxMUU2QjM5Q0I5NjNGNTg5RENBOSIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PsLhfAYAAAJKSURBVHja1JbHjuJAEIbHJgeRVwjEAUQS7/8yCAQcQCCRcw7zyTXqMaYtdrRz2Tq0inbV74q/MR6Px8evivnx2+J9a0ESnU5nvV7HYrFyuWwYxj/FeDweh8PharUClxOdG7sBbzqfz/YbQ1vH+/0+nU4nk4nDXyQYDP6xZLfbtVotv99fq9U4XRGx6/V6p9Ppqy5eLxCcvOZwOFwuF7kHolgsEvV2u/X5fPV6PRAI6BHJotlsXq/XdDpNIOFw2P4UUGInAxwNSx6WYNZoNFyzxk2M7AUlUvvPbreLGXqhUEDJZrOhUMgV0SGkNhqNcrlcPp9Xl7fbrd1uUyLTNIlOve+719RusVhoEaWbjp56PJ5KpUIFqe9gMHBOD2HP53MSocw/GGavl5RRGKz9fv+EOJvNpH2RSORHG5JKpWRuCOgJUUJLJBJvV+JV8OLcbDZPiDLJjkFRHaD8MqforwaSltoFU7lxUuZXB2ok1pzo2mrKmmn2Wt06dk6ru3mZaqU41ebZhVJQfmmCtizipfbatNcCItHWXtrl1jTxUkPyhRiPx+WZNsxMJgM5cmoDFERB+EZMJpPSFvv0K4lGo9VqlfP1kdjjC8ITIrvJ2qIsl8vxePyXk4gl9ij4guDsNcRFaij9fh++eguHDZYoeOGr53CmEk6WDWUT2Fkh0dfakaxER/chcFjD9asAKCSkJjlmCcRHUsLha0tUP0ulkh3OlR8hDjjRQV92YfrgSkheM2pujMv90hLWGWj5Bgg5JSxxG0/jP/hP8SnAAOceffMxdvQ4AAAAAElFTkSuQmCC');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    vertical-align: middle;
    margin-right: 0.03*2rem;
    position: relative;
    top: -0.01*2rem;
}
.payMainAli,.payMainApple,.payMainCfd {
    padding-bottom: 0.05*2rem;
    border-bottom: 1px solid #e5e5e5;
}
.payMainAli > span:last-child,.payMainApple > span:last-child ,.payMainCfd > span:last-child{
    float: right;
    display: none;
    width: 0.15*2rem;
    height: 0.15*2rem;
    border: 1px solid @blue;
    border-radius: 50%;
    -webkit-border-radius: 50%;
    vertical-align: middle;
}
.payMainAli > span:last-child ins,.payMainApple > span:last-child ins,.payMainCfd > span:last-child ins {
    display: inline-block;
    width: 0.1*2rem;
    height: 0.05*2rem;
    border-left: 1px solid @blue;
    border-bottom: 1px solid @blue;
    transform: rotate(-45deg);
    -webkit-transform: rotate(-45deg);
    vertical-align: top;
    position: relative;
    top: 0.03*2rem;
    left: 0.02*2rem;
}
.payMainBank {
    padding-top: 0.15*2rem;
    padding-bottom: 0.05*2rem;
    border-bottom: 1px solid #e5e5e5;
}
.payMainWeixin {
    padding-bottom: 0.05*2rem;
    border-bottom: 1px solid #e5e5e5;
}
.payMainWeixin > span:last-child {
    float: right;
    display: none;
    width: 0.15*2rem;
    height: 0.15*2rem;
    border: 1px solid @blue;
    border-radius: 50%;
    -webkit-border-radius: 50%;
    vertical-align: middle;
}
.payMainWeixin > span:last-child ins {
    display: inline-block;
    width: 0.1*2rem;
    height: 0.05*2rem;
    border-left: 1px solid @blue;
    border-bottom: 1px solid @blue;
    transform: rotate(-45deg);
    -webkit-transform: rotate(-45deg);
    vertical-align: top;
    position: relative;
    top: 0.03*2rem;
    left: 0.02*2rem;
}
.payMainBank > span:last-child {
    float: right;
    display: none;
    width: 0.15*2rem;
    height: 0.15*2rem;
    border: 1px solid @blue;
    border-radius: 50%;
    -webkit-border-radius: 50%;
    vertical-align: middle;
}
.payMainBank > span:last-child ins {
    display: inline-block;
    width: 0.1*2rem;
    height: 0.05*2rem;
    border-left: 1px solid @blue;
    border-bottom: 1px solid @blue;
    transform: rotate(-45deg);
    -webkit-transform: rotate(-45deg);
    vertical-align: top;
    position: relative;
    top: 0.03*2rem;
    left: 0.02*2rem;
}
.payMainPrice {
    padding-top: 0.15*2rem;
    padding-bottom: 0.05*2rem;
    border-bottom: 1px solid #e5e5e5;
}
.payMainPrice > span:last-child {
    float: right;
}
.payMainPrice > span:last-child ins {
    text-decoration: none;
    font-size: 0.16*2rem;
    // color: @blue;
}
.payMainProtocol {
    padding: 0.15*2rem 0;
    border-bottom: 1px solid #e5e5e5;
}
.payMainProtocol > span:first-child {
    display: inline-block;
    width: 0.14*2rem;
    height: 0.14*2rem;
    background: url(//i.thsi.cn/sns/circle/wapcircle/img2/askChat/icon-matchsuc_small.png) no-repeat;
    background-size: contain;
    border-radius: 50%;
    vertical-align: middle;
    margin-right: 0.03*2rem;
    margin-top: -0.03*2rem;
}
.notselected{
    width: 0.14*2rem;
    height: 0.14*2rem;
    background: url(//i.thsi.cn/sns/circle/wapcircle/img2/askChat/icon-matchsuc-notselected.png) no-repeat!important;
    background-size: cover!important;
    margin-top: -0.03*2rem;
    border-radius: 0;
}
// .payMainProtocol > span:first-child ins {
//     display: inline-block;
//     width: 0.1*2rem;
//     height: 0.05*2rem;
//     border-left: 1px solid #fff;
//     border-bottom: 1px solid #fff;
//     transform: rotate(-45deg);
//     -webkit-transform: rotate(-45deg);
//     vertical-align: top;
//     position: relative;
//     top: 0.03*2rem;
//     left: 0.02*2rem;
// }
.payMainProtocol i {
    color: @blue;
    font-style: normal;
}
.payMainBuy {
    text-align: center;
}
.payMainBuy span {
    display: inline-block;
    width: 85%;
    padding: 0.08*2rem 0;
    font-size: 0.16*2rem;
    color: #fff;
    text-align: center;
    border-radius: 0.04*2rem;
    -webkit-border-radius: 0.04*2rem;
}

.payWaitDetail {
    padding-bottom: 0.1*2rem;
    border-bottom: 1px solid #e5e5e5;
}
.payWaitDetail > span:last-child {
    float: right;
    color: #cccccc;
    font-size: 0.14*2rem;
}
.payWaitDetail > span:last-child ins {
    display: inline-block;
    text-decoration: none;
    width: 0.15*2rem;
    height: 0.15*2rem;
    background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABsAAAAcCAIAAAAfs1O6AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyJpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMy1jMDExIDY2LjE0NTY2MSwgMjAxMi8wMi8wNi0xNDo1NjoyNyAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNiAoV2luZG93cykiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6ODg0QzkyNjkzN0JEMTFFNkIzOUNCOTYzRjU4OURDQTkiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6ODg0QzkyNkEzN0JEMTFFNkIzOUNCOTYzRjU4OURDQTkiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDo4ODRDOTI2NzM3QkQxMUU2QjM5Q0I5NjNGNTg5RENBOSIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDo4ODRDOTI2ODM3QkQxMUU2QjM5Q0I5NjNGNTg5RENBOSIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PsLhfAYAAAJKSURBVHja1JbHjuJAEIbHJgeRVwjEAUQS7/8yCAQcQCCRcw7zyTXqMaYtdrRz2Tq0inbV74q/MR6Px8evivnx2+J9a0ESnU5nvV7HYrFyuWwYxj/FeDweh8PharUClxOdG7sBbzqfz/YbQ1vH+/0+nU4nk4nDXyQYDP6xZLfbtVotv99fq9U4XRGx6/V6p9Ppqy5eLxCcvOZwOFwuF7kHolgsEvV2u/X5fPV6PRAI6BHJotlsXq/XdDpNIOFw2P4UUGInAxwNSx6WYNZoNFyzxk2M7AUlUvvPbreLGXqhUEDJZrOhUMgV0SGkNhqNcrlcPp9Xl7fbrd1uUyLTNIlOve+719RusVhoEaWbjp56PJ5KpUIFqe9gMHBOD2HP53MSocw/GGavl5RRGKz9fv+EOJvNpH2RSORHG5JKpWRuCOgJUUJLJBJvV+JV8OLcbDZPiDLJjkFRHaD8MqforwaSltoFU7lxUuZXB2ok1pzo2mrKmmn2Wt06dk6ru3mZaqU41ebZhVJQfmmCtizipfbatNcCItHWXtrl1jTxUkPyhRiPx+WZNsxMJgM5cmoDFERB+EZMJpPSFvv0K4lGo9VqlfP1kdjjC8ITIrvJ2qIsl8vxePyXk4gl9ij4guDsNcRFaij9fh++eguHDZYoeOGr53CmEk6WDWUT2Fkh0dfakaxER/chcFjD9asAKCSkJjlmCcRHUsLha0tUP0ulkh3OlR8hDjjRQV92YfrgSkheM2pujMv90hLWGWj5Bgg5JSxxG0/jP/hP8SnAAOceffMxdvQ4AAAAAElFTkSuQmCC');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    vertical-align: middle;
    margin-right: 0.03*2rem;
    position: relative;
    top: -0.01*2rem;
}
.payWaitCont {
    text-align: center;
}
.payWaitCont > div:first-child .ins {
    display: block;
    margin: 0 auto;
    width: 0.6*2rem;
    height: 0.6*2rem;
}
.payWaitCont > div:last-child {
    font-size: 0.16*3rem;
}
.icon-payloading{
    display: inline-block;
    width: 17px;
    height: 3px;
    background: url(//i.thsi.cn/sns/circle/wapcircle/img2/pkg/icon-payloading.gif) no-repeat;
    background-size: 100% 100%;
    position: relative;
    top: 3px;
}
.payWaitTip {
    padding: 0.15*2rem 0;
    font-size: 0.12*2rem;
    text-align: left;
    color: #777777;
    border-top: 1px solid #e5e5e5;
}
.payWaitBtn {
    text-align: center;
}
.payWaitBtn span {
    color: #fff;
    font-size: 0.16*2rem;
    text-align: center;
    display: inline-block;
    width: 45%;
    padding: 0.05*2rem 0;
    background-color: @blue;
    border-radius: 0.04*2rem;
    -webkit-border-radius: 0.04*2rem;
}
.payWaitBtn > span:first-child {
    background-color: #cccccc;
    margin-right: 6%;
}

.payTimeoutDetail {
    padding-bottom: 0.1*2rem;
    border-bottom: 1px solid #e5e5e5;
}
.payTimeoutDetail > span:last-child {
    float: right;
    color: #e93030;
    font-size: 0.14*2rem;
}
.payTimeoutDetail > span:last-child ins {
    display: inline-block;
    text-decoration: none;
    width: 0.15*2rem;
    height: 0.15*2rem;
    background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABsAAAAcCAIAAAAfs1O6AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyJpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMy1jMDExIDY2LjE0NTY2MSwgMjAxMi8wMi8wNi0xNDo1NjoyNyAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNiAoV2luZG93cykiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6ODg0QzkyNjkzN0JEMTFFNkIzOUNCOTYzRjU4OURDQTkiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6ODg0QzkyNkEzN0JEMTFFNkIzOUNCOTYzRjU4OURDQTkiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDo4ODRDOTI2NzM3QkQxMUU2QjM5Q0I5NjNGNTg5RENBOSIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDo4ODRDOTI2ODM3QkQxMUU2QjM5Q0I5NjNGNTg5RENBOSIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PsLhfAYAAAJKSURBVHja1JbHjuJAEIbHJgeRVwjEAUQS7/8yCAQcQCCRcw7zyTXqMaYtdrRz2Tq0inbV74q/MR6Px8evivnx2+J9a0ESnU5nvV7HYrFyuWwYxj/FeDweh8PharUClxOdG7sBbzqfz/YbQ1vH+/0+nU4nk4nDXyQYDP6xZLfbtVotv99fq9U4XRGx6/V6p9Ppqy5eLxCcvOZwOFwuF7kHolgsEvV2u/X5fPV6PRAI6BHJotlsXq/XdDpNIOFw2P4UUGInAxwNSx6WYNZoNFyzxk2M7AUlUvvPbreLGXqhUEDJZrOhUMgV0SGkNhqNcrlcPp9Xl7fbrd1uUyLTNIlOve+719RusVhoEaWbjp56PJ5KpUIFqe9gMHBOD2HP53MSocw/GGavl5RRGKz9fv+EOJvNpH2RSORHG5JKpWRuCOgJUUJLJBJvV+JV8OLcbDZPiDLJjkFRHaD8MqforwaSltoFU7lxUuZXB2ok1pzo2mrKmmn2Wt06dk6ru3mZaqU41ebZhVJQfmmCtizipfbatNcCItHWXtrl1jTxUkPyhRiPx+WZNsxMJgM5cmoDFERB+EZMJpPSFvv0K4lGo9VqlfP1kdjjC8ITIrvJ2qIsl8vxePyXk4gl9ij4guDsNcRFaij9fh++eguHDZYoeOGr53CmEk6WDWUT2Fkh0dfakaxER/chcFjD9asAKCSkJjlmCcRHUsLha0tUP0ulkh3OlR8hDjjRQV92YfrgSkheM2pujMv90hLWGWj5Bgg5JSxxG0/jP/hP8SnAAOceffMxdvQ4AAAAAElFTkSuQmCC');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    vertical-align: middle;
    margin-right: 0.03*2rem;
    position: relative;
    top: -0.01*2rem;
}
.payTimeoutCont {
    padding-top: 0.2*2rem;
    padding-bottom: 0.35*2rem;
    border-bottom: 1px solid #e5e5e5;
    text-align: center;
    font-size: 0.16*2rem;
}
.payTimeoutCont > div:first-child {
    padding-bottom: 0.1*2rem;
}
.payTimeoutCont > div:first-child ins {
    display: inline-block;
    width: 0.6*2rem;
    height: 0.6*2rem;
    background-image: url('data:image/jpg;base64,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');
    background-repeat: no-repeat;
    background-size: 100% 100%;
}
.payTimeoutCont > div:nth-child(2) {
    padding-bottom: 0.1*2rem;
}
.payTimeoutBtn {
    text-align: center;
    padding-top: 0.2*2rem;
}
.payTimeoutBtn span {
    color: #fff;
    font-size: 0.16*2rem;
    text-align: center;
    display: inline-block;
    width: 45%;
    padding: 0.05*2rem 0;
    background-color: @blue;
    border-radius: 0.04*2rem;
    -webkit-border-radius: 0.04*2rem;
}
.payTimeoutBtn > span:first-child {
    background-color: #cccccc;
    margin-right: 6%;
}

.paySure > div:first-child {
    padding-bottom: 0.1*2rem;
    border-bottom: 1px solid #e5e5e5;
}
.paySureCont {
    padding: 0.2*2rem 0;
    text-align: center;
    border-bottom: 1px solid #e5e5e5;
}
.paySureCont > div:first-child {
    padding-bottom: 0.1*2rem;
}
.paySureCont > div:first-child ins {
    display: inline-block;
    width: 0.6*2rem;
    height: 0.6*2rem;
    background-image: url('data:image/jpg;base64,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');
    background-repeat: no-repeat;
    background-size: 100% 100%;
}
.paySureCont > div:last-child {
    font-size: 0.16*2rem;
}
.paySureBtn {
    padding-top: 0.15*2rem;
    text-align: center;
}
.paySureBtn span {
    color: #fff;
    font-size: 0.16*2rem;
    text-align: center;
    display: inline-block;
    width: 45%;
    padding: 0.05*2rem 0;
    background-color: @blue;
    border-radius: 0.04*2rem;
    -webkit-border-radius: 0.04*2rem;
}
.paySureBtn > span:first-child {
    background-color: #cccccc;
    margin-right: 6%;
}
.payMain{
    padding: 0.1*2rem 0;
    //订阅方式
    .payMainMode{
        border-bottom: 1px solid #e5e5e5;
        .redtag{
            font-size: 0.10*2rem;
            color: #e93030;
            border: 1px solid #e93030;
            border-radius: 0.03*2rem;
            padding: 0.02*2rem 0.04*2rem;
            margin-right: 0.05*2rem;
            vertical-align: top;
        }
        img{
            margin-left: 0.05*2rem;
            width: 0.07*2rem;
        }
    }
    //服务详情
    .payMainInfo{
        border-bottom: 1px solid #e5e5e5;
        img{
            margin-left: 0.05*2rem;
            width: 0.07*2rem;
        }
    }
    .payMainDetail{
        padding: 0.01*2rem 0 0.11*2rem 0;
        margin: 0 0.1*2rem;
    }

    .only-post{
        width: 100%;
        height: 0.43*2rem;
        // margin-top: 0.15*2rem;
        padding: 0 0.1*2rem;
        box-sizing: border-box;
        border-radius: 0.02*2rem;

        .only-post-in{
            border:1px solid #ddd;
            //border-top: none;
            overflow: hidden;
            height: 0.43*2rem;
            line-height: 0.43*2rem;
            padding-left: 0.15*2rem;
            padding-right: 0.2*2rem;
            box-sizing: border-box;
            border-radius: 0.02*2rem;
        }
        .active{
            background: #fdebeb;
            border-color: #fdebeb;
            color: @red !important;
            background-image: url(//i.thsi.cn/sns/circle/img3/package/icon-priceselected.png);
            background-repeat: no-repeat;
            background-position: right bottom;
            background-size: 0.18*2rem 0.18*2rem;
            //border: none;
        }
    }

    .package-push{
        padding-left: 0.1*2rem;
        line-height: 1;
        margin-top: 0.15*2rem;
        .redtext{
            width: 0.36*2rem;
            height: 0.17*2rem;
            text-align: center;
            line-height: 0.17*2rem;
            border:1px solid @red;
            margin-right: 0.06*2rem;
            border-radius: 0.02*2rem;
            padding: 0.02*2rem 0.06*2rem;
        }
        .package-push-item{
            color: @32;
        }
    }

    .payModel-item{
        padding: 0.11*2rem 0;
        margin: 0 0.1*2rem;
        line-height: 0.21*2rem;
        font-size: 0.14*2rem;
        color: @32!important;
    }
    .payMainYhq{
        margin: 0;
        padding: 0.11*2rem 0.1*2rem;
    }
    .coupon-arrow-icon{
        margin-right: 0.04*2rem;
    }
    .payMainProtocol{
        padding: 0.15*2rem 0.1*2rem;
    }
    .payMainProtocol i{
        .bluetext();
    }
    .payMainBuy{
        padding: 0.2*2rem 0.2*2rem 0.1*2rem 0.1*2rem;
        span{
            width: 100%;
            padding: 0;
            height: 0.35*2rem;
            line-height: 0.35*2rem;
            background: @blue;
        }
    }
}
.payTimeout,.payWait{
    padding: 0;
    .payTimeoutCont,.payWaitCont{
        padding: 0.35*2rem 0;
    }
    .timeouttip{
        font-size: 0.24*2rem;
        line-height: 0.34*2rem;
        padding-bottom: 0;
    }
    .payTimeoutContinue{
        height: 0.35*2rem;
        line-height: 0.35*2rem;
        border-radius: 0.04*2rem;
        .cfff();
        .bluebg();
    }
    .icon-timeout{
        display: inline-block;
        width: 0.25*2rem;
        height: 0.25*2rem;
        background: url(//i.thsi.cn/sns/circle/wapcircle/coupon/booktip.png) no-repeat;
        background-size: 100% 100%;
        position: relative;
        top: 0.06*2rem;
    }
    .payTimeoutBtn{
        padding: 0.2*2rem 0.1*2rem;
    }
}
.payWaitBtn-inner{
    padding: 0.2*2rem 0.1*2rem;
    .payWaitContinue{
        background: #999!important;
    }
    span{
       .bluebg();
    }
}
.clsigngray{
    background: #949494!important;
}
//标题
.left-arrow{
    display: inline-block;
    width: 0.1*2rem;
    height: 0.1*2rem;
    border-top: 1px solid #999;
    border-left: 1px solid #999;
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
    margin-left: 0.02*2rem;
    margin-right: 0.05*2rem;
}
//协议列表
.pay-protocol-list{
    padding-bottom: 0;
    .protocol-list{
        margin-top: 0.1*2rem;
        li{
            height: 0.44*2rem;
            line-height: 0.44*2rem;
            text-align: center;
            font-size: 0.14*2rem;
            color: #333;
            border-top: 1px solid #eee;
        }
    }
}
//订阅方式列表
.payMainPkgMode{
    border-bottom: 1px solid #e5e5e5;
    margin: 0 0.1*2rem;
    .mode-list-ul{
        padding: 0.15*2rem 0;
        ul{
            padding: 0.1*2rem;
        }
        li{
            // box-shadow: 0 0 0.05*2rem 0.03*2rem #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 0.02*2rem;
            position: relative;
            overflow: hidden;
            padding: 0 0.11*2rem;
            background: #fff;
            width: 24%;
            .origin-price,.prefer-price{
                float: left;
                height: 100%;
                width: 100%;
            }
            .prefer-price{
                .title-top{
                    line-height: 0.12*2rem;
                    padding-top: 0.18*2rem;
                }
                .title-now{
                    .c666();
                    line-height: 0.18*2rem;
                    padding-top: 0.14*2rem;
                }
                .title-ori{
                    .c999();
                    line-height: 0.12*2rem;
                    padding-top: 0.09*2rem;
                    padding-bottom: 0.17*2rem;
                    text-decoration: line-through;
                }
            }
            .origin-price{
                .title-top{
                    .c666();
                    line-height: 0.12*2rem;
                    padding-top: 0.28*2rem;
                }
                .title-bot{
                    .c666();
                    padding-top: 0.15*2rem;
                    line-height: 0.18*2rem;
                    padding-bottom: 0.27*2rem;
                }
            }
            &.active{
                background: #fdebeb;
                border-color: #fdebeb;
                .title-top,.title-bot,.title-ori{
                    color: @red!important;
                }
                .title-now{
                    .redtext();
                }
                background-image: url(//i.thsi.cn/sns/circle/img3/package/icon-priceselected.png);
                background-repeat: no-repeat;
                background-position: right bottom;
                background-size: 0.18*2rem 0.18*2rem;
            }
            &.center{
                margin: 0 3% 0 3%;
            }
            .recommend{
                position: absolute;
                background: red;
                top: 0.02*2rem;
                font-size: 0.10*2rem;
                right: -0.15*2rem;
                transform: rotate(45deg);
                width: 0.5*2rem;
                text-align: center;
            }
        }
    }
    .mode-comfirm-btn{
        background: @blue;
        text-align: center;
        font-size: 0.14*2rem;
        color: #fff;
        padding: 0.08*2rem 0;
        border-radius: 0.03*2rem;
    }
}
//服务详情
.pay-info-list{
    .info-list-ul{
        margin-top: 0.1*2rem;
        border-top: 1px solid #eee;
        img{
            margin-right: 0.1*2rem;
            width: 0.16*2rem;
        }
        .info-title{
            padding-top: 0.2*2rem;
            padding-bottom: 0.13*2rem;
        }
        .info-desc{
            padding-left: 0.25*2rem;
        }
    }
}
//退出支付弹窗
.payQuit{
    width:2.8*2rem;
    height: 3*2rem;
    position: fixed;
    top:50%;
    left: 50%;
    margin-left: -1.4*2rem;
    margin-top: -1.5*2rem;
    z-index: 31;
    background: #fff;
    border-radius: 0.03*2rem;
    overflow: hidden;
    .pay-quit-wrap{
        padding: 0.12*2rem;
        position: relative;
        .quit-close{
            position: absolute;
            right: 0.15*2rem;
            top: 0.10*2rem;
            width: 0.15*2rem;
            height: 0.15*2rem;
            img{
                width: 100%;
            }
        }
        .quit-logo{
            width: 1.5*2rem;
            margin: 0.2*2rem auto;
            text-align: center;
            img{
                width: 100%;
            }
        }
        .quit-tip{
            text-align: center;
        }

    }
    .quit-btn-wrap{
        border-top: 1px solid #eee;
        margin-top: 0.4rem;
        padding-top: 0.1*2rem;
        .quit-cancel{
            width: 48%;
            color: @blue;
        }
        .quit-sure{
            width: 48%;
            border-right: 1px solid #eee;
        }
    }
}
//协议通用 容器
.protocol-main-wrap{
    width: 100%;
    height: 100%;
    position: fixed;
    top: 100%;
    left: 0;
    background-color: #fff;
    display: none;
    overflow-y:scroll;
    overflow-x:hidden;
    z-index: 33;
    .probox{
        text-align: justify;
        padding: 20px 16px 50px 16px;
        .title{
            line-height: 20px;
            margin-bottom: 15px;
        }
    }
    .clTip {
        padding: 0.2*2rem 0.1*2rem 0.5*2rem 0.1*2rem;
        color: @32;
        font-size: 0.14*2rem;
    }
    .clTip h1 {
        font-size: 0.18*2rem;
        font-weight: bold;
        text-align: center;
        padding: 0.25*2rem 0 0.1*2rem;
    }
    .clTip h2 {
        padding:0.1*2rem 0;
    }
    .clTip h3 {
        margin-top: 0.15*2rem;
        text-align: center;
        font-weight: bold;
        clear: both;
        padding-top: 0.1*2rem;
    }
    .clTip p {
        text-align: justify;
        text-indent: 2em;
    }
    .clTip p.clear {
        text-indent: 0em;
    }
    .clTip p.clear:after {
        content: '';
        display: block;
        clear: both;
    }
    .clTip p.left {
        text-align: left;
    }
    .clTip p.clear > span {
        display: inline-block;
        float: right;
        width: 78%;
    }
    .clTip p.clear > span:first-child {
        display: inline-block;
        float: left;
        width: 17%;
        text-align: right;
        margin-right: 2%;
    }
    .bold {
        font-weight: bold;
    }
    .marginBottom {
        margin-bottom: 0.15*2rem;
    }
    .marginTop {
        margin-top: 0.15*2rem;
    }
    .del {
        text-decoration: underline;
    }
    .clTipRead{
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        background-color: #fff;
        text-align: center;
        padding: 0.05*2rem 0;
        display: none;
        -webkit-transform:translateZ(0);//安卓 华为下有bug
    }
    .clTipRead span {
        display: inline-block;
        background-color: @blue;
        font-size: 0.16*2rem;
        color: #fff;
        text-align: center;
        border-radius: 0.04*2rem;
        -webkit-border-radius: 0.04*2rem;
        padding: 0.08*2rem 0;
        width: 80%;
    }
    .clTipRead span {
        width: 80%;
    }
    .clTipReadUrl {
        display: none;
    }
}
// 机器人回复loading
@-webkit-keyframes ball-beat {
    50% {
        opacity: 0.2;
        -webkit-transform: scale(0.75);
        transform: scale(0.75);
    }

    100% {
        opacity: 1;
        -webkit-transform: scale(1);
        transform: scale(1);
    }
}

@keyframes ball-beat {
    50% {
        opacity: 0.2;
        -webkit-transform: scale(0.75);
        transform: scale(0.75);
    }

    100% {
        opacity: 1;
        -webkit-transform: scale(1);
        transform: scale(1);
    }
}
.ball-beat{
    display: inline-block!important;
    line-height: 0;
    .ball-item {
        background-color: #323232;
        width: 0.08*2rem;
        height: 0.08*2rem;
        border-radius: 50%;
        margin-right: 0.04*2rem;
    }
    .ball-item1{
        animation-name: ball-beat;
        animation-duration: 0.7s;
        animation-timing-function: linear;
        animation-delay: 0.18s;
        animation-iteration-count: infinite;
    }
    .ball-item2{
        animation-name: ball-beat;
        animation-duration: 0.7s;
        animation-timing-function: linear;
        animation-delay: 0.36s;
        animation-iteration-count: infinite;
    }
    .ball-item3{
        animation-name: ball-beat;
        animation-duration: 0.7s;
        animation-timing-function: linear;
        animation-delay: 0.54s;
        animation-iteration-count: infinite;
    }
}
.llpay-tips{
    background: #FFF3EF;
    font-size: 0.1*2rem;
    color: #FF8218;
    padding: 0.08*2rem 0.1*2rem;
}
