let URL = {};
let baseURL = '';
if (process.env.NODE_ENV === 'development') {
  baseURL = '//yapi.myhexin.com/yapi';
  URL = {
    newsStockUrl: `${baseURL}/mock/5019/tapp/news/push/stock`,
    useNewTabUrl: `${baseURL}/mock_v2/310388/app/flash/switch/useNewTab`,
    pushTabUrl: `${baseURL}/mock/7715/app/flash/flashnews/v1/tab?role=kuaixun`,
    newPushTabUrl: `${baseURL}/mock_v2/310388/app/flash/flashnews/v2/tab`,
    etfTabUrl: '//testfund.10jqka.com.cn/marketing/operation/config/module/v1/key/etfzoneflashnews',
    pushListUrl: `${baseURL}/mock_v2/310388/app/flash/flashnews/v1/list`,
    refreshListUrl: `${baseURL}/mock/7715/app/flash/flashnews/v1/refresh`,
  };
} else {
  URL = {
    newsStockUrl: `${window.location.origin}/tapp/news/push/stock/`,
    useNewTabUrl: `${window.location.origin}/app/flash/switch/useNewTab`,
    pushTabUrl: `${window.location.origin}/app/flash/flashnews/v1/tab?role=kuaixun`,
    newPushTabUrl: `${window.location.origin}/app/flash/flashnews/v2/tab`,
    etfTabUrl: '//fund.10jqka.com.cn/marketing/operation/config/module/v1/key/etfzoneflashnews',
    pushListUrl: `${window.location.origin}/app/flash/flashnews/v1/list`,
    refreshListUrl: `${window.location.origin}/app/flash/flashnews/v1/refresh`,
  };
}

export default URL;
