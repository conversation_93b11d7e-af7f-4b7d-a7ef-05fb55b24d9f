import { useRequest, setGlobalOptions } from '@thsf2e/useRequest';
import URL from './url';
//全局格式化规则
setGlobalOptions({
  dataFormat: res => {
    const { data, status_code } = res;
    if (status_code === 0) {
      return data;
    } else {
      throw new Error(res.status_msg);
    }
  },
  plugins: {
    // response: config => {
    //   const { url, result, error } = config;
    //   if(error)[
    //     alert(error)
    //   ]
    // console.log(url, result, error);
    // },
  },
});
// 是否用新tab接口
export const useNewTab = () => {
  return useRequest({
    url: URL.useNewTabUrl,
  });
};

// 获取tab列表
export const getTabList = () => {
  return useRequest({
    url: URL.pushTabUrl,
  });
};

// 获取新tab列表
export const getNewTabList = () => {
  return useRequest({
    url: URL.newPushTabUrl,
  });
};

// 获取ETF tab列表
export const getEtfTabList = () => {
  return useRequest({
    url: URL.etfTabUrl,
  });
};

// 获取快讯列表
export const getPushList = params => {
  return useRequest({
    url: URL.pushListUrl,
    urlParams: { ...params },
  });
};

// 获取定时更新列表
export const getRefreshList = params => {
  return useRequest({
    url: URL.refreshListUrl,
    urlParams: { ...params },
  });
};
