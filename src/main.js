import Vue from 'vue';
import App from './App.vue';
import {
  initFontSize,
  sns
} from '@/common/js/sns.js';
import Emitter from '@/common/js/emitter';
// initFontSize 方法应该直接执行，不用挂在created和mounted里面，不然会出现hxmui中组件样式问题，如果因为这个方法引起一些问题，请联系沈佳棋
initFontSize();

const Bus = new Vue();
import Switch from 'thsc-hxmui/lib/Switch.js';
import Dialog from 'thsc-hxmui/lib/Dialog.js';
import Toast from 'thsc-hxmui/lib/Toast.js';
import Overlay from 'thsc-hxmui/lib/Overlay.js';
import 'thsc-hxmui/lib/style/base.css';
import 'thsc-hxmui/lib/style/Switch.css';
import 'thsc-hxmui/lib/style/Dialog.css';
import 'thsc-hxmui/lib/style/Toast.css';
import 'thsc-hxmui/lib/style/Overlay.css';
import registerThemeMode from 'thsc-hxmui/lib/theme';

registerThemeMode(Vue, 'auto');
Vue.use(Switch).use(Dialog).use(Toast).use(Overlay);
Vue.prototype.$bus = Bus;
Vue.prototype.$base = window['thsc-sns-baselib'];
Vue.prototype.$sns = sns;
Vue.mixin(Emitter);
// 判断是页面所处环境 页面pid
Vue.prototype.$pid = 'free_zixun_724';

const ua = window.navigator.userAgent;
const themeReg = /hxtheme\/(\S*)/;
if (ua.match(themeReg) && ua.match(themeReg)[1] == 1) {
  Vue.prototype.$displayMode = 1;
} else {
  Vue.prototype.$displayMode = 2;
}

Vue.config.productionTip = false;

Vue.config.devtools = true

new Vue({
  render: h => h(App),
}).$mount('#app');
