<template>
  <div>
    <container></container>
  </div>
</template>

<script>
import container from './pages/Main.vue';
import getEnv from '@ths/getEnv';

const env = getEnv(navigator.userAgent);

export default {
  name: 'App',
  components: {
    container,
  },
  data() {
    return {};
  },
  mounted() {
    this.setHeight(); //解决安卓最小高度问题
  },
  methods: {
    setHeight() {
      this.$nextTick(() => {
        const body = document.documentElement || document.body;
        if (env.sys === 'adr') {
          body.style['min-height'] = `${screen.availHeight}px`;
        }
      });
    },
  },
};
</script>
<style lang="less" scoped></style>
