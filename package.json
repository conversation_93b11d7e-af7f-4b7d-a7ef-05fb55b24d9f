{"name": "flashnews", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "release": "vue-cli-service build --mode release", "testing": "vue-cli-service build --mode testing", "lint": "vue-cli-service lint", "format": "prettier --write \"src/**/*.ts\" \"src/**/*.vue\"", "fix": "eslint --fix ."}, "dependencies": {"@babel/runtime-corejs3": "^7.14.7", "@ths-m/HXKline": "1.2.3-beta.2", "@ths/getEnv": "^0.3.1", "@ths/stat": "^0.2.0", "@ths/vue-stat": "^0.1.0", "@thsf2e/useRequest": "^1.0.8", "@thsf2e/useelk": "^2.0.1", "axios": "^0.18.0", "dayjs": "^1.11.3", "fastclick": "^1.0.6", "html2canvas": "^1.0.0-alpha.12", "jsonp": "^0.2.1", "lodash": "^4.17.11", "postcss-plugin-px2rem": "^0.8.1", "postcss-preset-env": "^6.7.0", "qrcodejs2": "0.0.2", "string-similarity": "^4.0.4", "thsc-hxmui": "^1.4.9", "vconsole": "^3.14.6", "vue-awesome-swiper": "^3.1.3", "vue-class-component": "^7.2.6", "vue-property-decorator": "^9.1.2"}, "devDependencies": {"@babel/plugin-transform-modules-commonjs": "^7.23.0", "@babel/preset-env": "^7.23.2", "@vue/cli-plugin-babel": "^3.1.1", "@vue/cli-plugin-eslint": "^3.1.5", "@vue/cli-service": "^3.1.4", "autoprefixer": "^9.4.4", "babel-eslint": "^10.0.1", "crypto-js": "^4.0.0", "dom-serializer": "^1.3.2", "domutils": "^2.8.0", "eslint": "^7.23.0", "eslint-config-sonarjs": "^1.0.19", "eslint-plugin-vue": "^5.0.0-0", "extract-text-webpack-plugin": "^3.0.2", "gulp": "~3.9.1", "gulp-clean": "~0.4.0", "gulp-file-copy": "0.0.1", "gulp-zip": "~4.1.0", "htmlparser2": "^6.1.0", "jszip": "^3.6.0", "less": "^2.7.3", "less-loader": "^4.1.0", "md5": "^2.3.0", "style-resources-loader": "^1.2.1", "vue-cli-plugin-style-resources-loader": "^0.1.3", "vue-head": "^2.1.0", "vue-template-compiler": "^2.5.17"}}