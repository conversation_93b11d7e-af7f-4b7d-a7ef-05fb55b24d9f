server {
	listen 80 default backlog=8192;
	# server_name xtz.10jqka.com.cn;
	index index.html index.htm;
	root /var/www/html/dist/;

	location /readiness {
		default_type "text/html";
		return 200 "ok";
	}

    location / {
        index index.html index.htm;

		# if (!-e $request_filename) {
		# 	proxy_pass http://website-tzkt-deployment:8080;
		# }
		proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forward-For $proxy_add_x_forwarded_for;
    }
}