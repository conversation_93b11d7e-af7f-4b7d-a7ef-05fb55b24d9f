module.exports = {
  root: true,
  env: {
    node: true,
    browser: true,
    es6: true,
  },
  extends: ['plugin:vue/essential','eslint:recommended'],
  parserOptions: {
    'parser': 'babel-eslint'
  },
  rules: {
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'new-cap': 'off',
    'no-unreachable-loop': 'off',
    'default-case-last': 'off',
    'vue/experimental-script-setup-vars': 'off',
    'no-magic-numbers': 'off',
    'no-setter-return': 'off',
    'default-param-last': 'off',
    'no-dupe-else-if': 'off',
    'no-undef': 'off',
    'no-empty-function': 'off',
    'filenames/match-exported': 'off',
    'no-invalid-this': 'off',
    'consistent-return': 'off',
    'eqeqeq': 'off',
    'line-comment-position': 'off',
    'default-case': 'off',
    'sonarjs/no-identical-functions': 'off',
    'no-unused-vars': 'off',
    'array-callback-return': 'off',
    'camelcase': 'off',
    'max-depth': 'off',
    'sonarjs/prefer-single-boolean-return': 'off',
    'vue/require-valid-default-prop': 'off',
    'no-prototype-builtins': 'off',
    'no-constant-condition': 'off',
    'vue/return-in-computed-property': 'off'
  },
};
